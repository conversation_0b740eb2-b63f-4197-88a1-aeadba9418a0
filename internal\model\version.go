package model

import "time"

// VersionModel 版本
type VersionModel struct {
	ID          int64     `json:"id" gorm:"column:id"`                              // 主键id
	Version     string    `json:"version" gorm:"column:version"`                    // 版本
	Desc        string    `json:"desc" gorm:"column:desc"`                          // 版本内容
	IsForce     int8      `json:"is_force" gorm:"column:is_force;default:-1"`       // 是否强制更新：-1否，1是
	Channel     string    `json:"channel" gorm:"column:channel"`                    // 客户端/渠道
	CreateAt    time.Time `json:"create_at" gorm:"column:create_at;autoCreateTime"` // 创建时间
	IsDelete    int8      `json:"is_delete" gorm:"column:is_delete;default:-1"`     // 删除标记：-1未删除，1删除
	DownloadUrl string    `json:"download_url" gorm:"column:download_url"`          // 下载地址
	VersionInt  int64     `json:"version_int" gorm:"column:version_int"`            // 版本int值
	UpdateAt    time.Time `json:"update_at" gorm:"column:update_at;autoUpdateTime"` // 更新时间
	IsRelease   int8      `json:"is_release" gorm:"column:is_release;default:-1"`   // 是否发布
}

// TableName 表名称
func (*VersionModel) TableName() string {
	return "version"
}
