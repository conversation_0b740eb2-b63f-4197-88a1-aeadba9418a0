package router

import (
	"chongli/internal/app/api/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type BusinessRouter struct {
	goodsCtrl         *controller.GoodsController
	payOrderCtrl      *controller.PayOrderController
	payNotifyCtrl     *controller.PayNotifyController
	JwtAuthMiddleware *middleware.JWTAuthMiddleware
}

func NewBusinessRouter(
	engine *gin.Engine,
	goodsCtrl *controller.GoodsController,
	payOrderCtrl *controller.PayOrderController,
	payNotifyCtrl *controller.PayNotifyController,
	JwtAuthMiddleware *middleware.JWTAuthMiddleware,
) *BusinessRouter {
	router := &BusinessRouter{
		payOrderCtrl:      payOrderCtrl,
		goodsCtrl:         goodsCtrl,
		payNotifyCtrl:     payNotifyCtrl,
		JwtAuthMiddleware: JwtAuthMiddleware,
	}

	// 支付相关路由
	pay := engine.Group("api/pay")
	router.InitPayNotify(pay)
	pay.Use(JwtAuthMiddleware.JWTAuth()) // 需要登录认证
	router.initPayOrder(pay)

	goods := engine.Group("api/goods")
	goods.Use(JwtAuthMiddleware.JWTAuth()) // 需要登录认证
	router.initGoods(goods)
	return router
}

func (r *BusinessRouter) InitPayNotify(pay *gin.RouterGroup) {
	// 支付回调
	pay.GET("notify", r.payNotifyCtrl.PayNotify)
}

func (r *BusinessRouter) initPayOrder(pay *gin.RouterGroup) {
	// 创建订单（下单）
	pay.POST("order", r.payOrderCtrl.CreateOrder)
}

func (r *BusinessRouter) initGoods(goods *gin.RouterGroup) {
	// 获取商品列表
	goods.GET("list", r.goodsCtrl.GetGoodsList)
}
