package repo

import (
	"chongli/internal/service/dto"

	"gorm.io/gorm"
)

// UserRepo 用户数据仓库接口
type UserRepo interface {
	// CreateUser 创建用户
	CreateUser(user *dto.UserInfoDto) error
	// GetUserInfoByDeviceId 根据 device_id 查询用户
	GetUserInfoByDeviceId(deviceId string) (*dto.UserInfoDto, error)
	// UpdateUsername 更新用户名
	UpdateUsername(userId int64, username string) error
	// UpdateUserInfoByDeviceId 根据 device_id 更新用户信息
	UpdateUserInfoByDeviceId(deviceId string, field map[string]any) error
	// UpdateUserInfoByUserId 根据 user_id 更新用户信息
	UpdateUserInfoByUserId(userId int64, field map[string]any, tx ...*gorm.DB) error
	// GetUserInfoByField 根据字段获取用户信息
	GetUserInfoByField(field, data string) (*dto.UserInfoDto, error)
	// IsNewUser 判断用户是否是新用户
	IsNewUser(field, data string) (bool, error)
	// IsCancelUser 判断用户是否已注销
	IsCancelUser(field, data string) (bool, error)
	// SetSmsCode 保存短信验证码
	SetSmsCode(phone, code string) (err error)
	// GetSmsCode 获取短信验证码
	GetSmsCode(phone string) (code string, err error)
	// GetSendCooling 获取发送冷却时间
	GetSendCooling(string) (bool, error)
	// SetSendCooling 设置发送冷却时间
	SetSendCooling(string) error
	// SetPhoneError 设置手机错误次数
	SetPhoneError(phone string) error
	// GetPhoneError 获取手机错误次数
	GetPhoneError(phone string) (int64, error)
	// GetUserInfoByUserId 根据 user_id 获取用户信息
	GetUserInfoByUserId(userId int64, tx ...*gorm.DB) (*dto.UserInfoDto, error)
	// DeleteUserByUserId 根据 user_id 删除用户
	DeleteUserByUserId(userId int64) error
	// UncancelUserByUserId 根据 user_id 恢复用户
	UncancelUserByUserId(userId int64) error
	// Page 分页获取用户列表
	Page(req *dto.GetUserListRequest) ([]*dto.UserInfoDto, int64, error)
	// GetAllVipUsers 获取所有VIP用户
	GetAllVipUsers() ([]*dto.UserInfoDto, error)
	// BatchUpdateUserInfoByIds 批量更新用户信息
	BatchUpdateUserInfoByIds(ids []int64, field map[string]any, tx ...*gorm.DB) error

	// 统计相关方法
	// GetUserStatistics 获取用户统计数据
	GetUserStatistics() (*dto.UserStatisticsDto, error)
	// GetTotalUsersCount 获取用户总量
	GetTotalUsersCount() (int64, error)
	// GetTotalUsersByChannel 获取各渠道用户量
	GetTotalUsersByChannel() ([]dto.ChannelUserCountDto, error)
	// GetMonthlyNewUsers 获取每月新增用户（当前年份1月到当前月）
	GetMonthlyNewUsers(year int) ([]dto.MonthlyUserStatsDto, error)
	// GetUsersByRegisterVersion 获取各注册版本用户量
	GetUsersByRegisterVersion() ([]dto.RegisterVersionCountDto, error)
	// GetYesterdayNewUsers 获取昨日新增用户量
	GetYesterdayNewUsers() (int64, error)
	// GetYesterdayNewUsersByChannel 获取昨日各渠道新增用户
	GetYesterdayNewUsersByChannel() ([]dto.ChannelUserCountDto, error)
	// GetThisMonthNewUsers 获取本月新增用户量
	GetThisMonthNewUsers() (int64, error)
	// GetThisMonthNewUsersByChannel 获取本月各渠道新增用户
	GetThisMonthNewUsersByChannel() ([]dto.ChannelUserCountDto, error)
	// GetTodayNewUsers 获取本日新增用户量
	GetTodayNewUsers() (int64, error)
	// GetTodayNewUsersByChannel 获取本日各渠道新增用户
	GetTodayNewUsersByChannel() ([]dto.ChannelUserCountDto, error)
	// GetThisMonthDailyNewUsers 获取本月每日新增用户统计
	GetThisMonthDailyNewUsers() ([]dto.DailyUserStatsDto, error)
}
