package controller

import (
	"chongli/component"
	"chongli/internal/dao"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// ConfigListRequest 配置列表请求
type ConfigListRequest struct {
	Page      int    `json:"page" form:"page" binding:"min=1"`         // 页码
	PageSize  int    `json:"size" form:"size" binding:"min=1,max=100"` // 每页大小
	Id        int64  `json:"id" form:"id"`                             // 主键id
	ConfigKey string `json:"config_key" form:"key"`                    // 配置名
	IsDelete  int8   `json:"is_delete" form:"is_delete"`               // 删除标志
	IsChannel int8   `json:"is_channel" form:"is_channel"`             // 是否是渠道配置
	IsPublish int8   `json:"is_publish" form:"is_publish"`             // 是否发布
}

// ConfigCreateRequest 创建配置请求
type ConfigCreateRequest struct {
	ConfigKey   string `json:"config_key" binding:"required,max=128"`    // 配置名
	ConfigValue string `json:"config_value" binding:"required,max=1024"` // 配置值
	Remark      string `json:"remark" binding:"max=255"`                 // 备注信息
	IsChannel   int8   `json:"is_channel" binding:"oneof=-1 1"`          // 是否是渠道配置
	IsPublish   int8   `json:"is_publish" binding:"oneof=-1 1"`          // 是否发布
}

// ConfigUpdateRequest 更新配置请求
type ConfigUpdateRequest struct {
	Id          int64  `json:"id" binding:"required,min=1"`              // 主键id
	ConfigKey   string `json:"config_key" binding:"required,max=128"`    // 配置名
	ConfigValue string `json:"config_value" binding:"required,max=1024"` // 配置值
	Remark      string `json:"remark" binding:"max=255"`                 // 备注信息
	IsChannel   int8   `json:"is_channel" binding:"oneof=-1 1"`          // 是否是渠道配置
	IsPublish   int8   `json:"is_publish" binding:"oneof=-1 1"`          // 是否发布
}

// ConfigResponse 配置响应
type ConfigResponse struct {
	Id          int64     `json:"id"`           // 主键id
	ConfigKey   string    `json:"config_key"`   // 配置名
	ConfigValue string    `json:"config_value"` // 配置值
	Remark      string    `json:"remark"`       // 备注信息
	CreateAt    time.Time `json:"create_at"`    // 创建时间
	IsDelete    int8      `json:"is_delete"`    // 删除标志
	IsChannel   int8      `json:"is_channel"`   // 是否是渠道配置
	IsPublish   int8      `json:"is_publish"`   // 是否发布
}

// ConfigListResponse 配置列表响应
type ConfigListResponse struct {
	List     []ConfigResponse `json:"list"`      // 配置列表
	Count    int64            `json:"count"`     // 总数
	Page     int              `json:"page"`      // 页码
	PageSize int              `json:"page_size"` // 每页大小
}

// ConfigController 配置控制器
type ConfigController struct {
	log       *logger.Logger
	repo      repo.ConfigRepo
	redisRepo repo.RedisRepo
}

// NewConfigController 创建配置控制器实例
func NewConfigController(bootStrap *component.BootStrap) *ConfigController {
	return &ConfigController{
		log:       bootStrap.Log,
		repo:      dao.NewConfigRepo(bootStrap),
		redisRepo: dao.NewRedisRepo(bootStrap),
	}
}

// convertToConfigResponse 将ConfigDto转换为ConfigResponse
func (c *ConfigController) convertToConfigResponse(configDto *dto.ConfigDto) *ConfigResponse {
	if configDto == nil {
		return nil
	}
	return &ConfigResponse{
		Id:          configDto.Id,
		ConfigKey:   configDto.ConfigKey,
		ConfigValue: configDto.ConfigValue,
		Remark:      configDto.Remark,
		CreateAt:    configDto.CreateAt,
		IsDelete:    int8(configDto.IsDelete),
		IsChannel:   int8(configDto.IsChannel),
		IsPublish:   int8(configDto.IsPublish),
	}
}

// convertToConfigQueryDto 将ConfigListRequest转换为ConfigQueryDto
func (c *ConfigController) convertToConfigQueryDto(req *ConfigListRequest) *dto.ConfigQueryDto {
	return &dto.ConfigQueryDto{
		Page:      req.Page,
		PageSize:  req.PageSize,
		Id:        req.Id,
		ConfigKey: req.ConfigKey,
		IsDelete:  model.StatusFlag(req.IsDelete),
		IsChannel: model.StatusFlag(req.IsChannel),
		IsPublish: model.StatusFlag(req.IsPublish),
	}
}

// convertToConfigCreateDto 将ConfigCreateRequest转换为ConfigCreateDto
func (c *ConfigController) convertToConfigCreateDto(req *ConfigCreateRequest) *dto.ConfigCreateDto {
	return &dto.ConfigCreateDto{
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		Remark:      req.Remark,
		IsChannel:   req.IsChannel,
		IsDelete:    -1, // 创建时默认为未删除
		IsPublish:   req.IsPublish,
	}
}

// convertToConfigUpdateDto 将ConfigUpdateRequest转换为ConfigUpdateDto
func (c *ConfigController) convertToConfigUpdateDto(req *ConfigUpdateRequest) *dto.ConfigUpdateDto {
	return &dto.ConfigUpdateDto{
		Id:          req.Id,
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		Remark:      req.Remark,
		IsChannel:   model.StatusFlag(req.IsChannel),
		IsDelete:    model.StatusFlag(-1), // 更新时默认为未删除
		IsPublish:   model.StatusFlag(req.IsPublish),
	}
}

// ConfigList 获取配置列表
func (c *ConfigController) ConfigList(ctx *gin.Context) {
	var req ConfigListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 转换为业务DTO
	queryDto := c.convertToConfigQueryDto(&req)

	// 获取列表
	configs, err := c.repo.GetConfigList(queryDto)
	if err != nil {
		c.log.Error("获取配置列表失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 获取总数
	total, err := c.repo.GetConfigCount(queryDto)
	if err != nil {
		c.log.Error("获取配置总数失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 转换为对外响应格式
	var responseList []ConfigResponse
	for _, config := range configs {
		if resp := c.convertToConfigResponse(config); resp != nil {
			responseList = append(responseList, *resp)
		}
	}

	response := ConfigListResponse{
		List:     responseList,
		Count:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    response,
	})
}

// ConfigDetail 获取配置详情
func (c *ConfigController) ConfigDetail(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "无效的配置ID",
		})
		return
	}

	// 获取配置信息
	config, err := c.repo.GetConfigByID(id)
	if err != nil {
		c.log.Error("获取配置详情失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if config == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    404,
			"message": "配置不存在",
		})
		return
	}

	// 转换为对外响应格式
	response := c.convertToConfigResponse(config)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    response,
	})
}

// ConfigAdd 创建配置
func (c *ConfigController) ConfigAdd(ctx *gin.Context) {
	var req ConfigCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 转换为业务DTO
	createDto := c.convertToConfigCreateDto(&req)

	// 创建配置
	config, err := c.repo.CreateConfig(createDto)
	if err != nil {
		c.log.Error("创建配置失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	// 删除所有旧的配置缓存
	if _, err := c.redisRepo.DelByPattern(ctx.Request.Context(), "chongli:config_cache:*"); err != nil {
		c.log.Error("清理配置缓存失败: %v", err)
	}

	// 转换为对外响应格式
	response := c.convertToConfigResponse(config)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    response,
	})
}

// ConfigUpdate 更新配置
func (c *ConfigController) ConfigUpdate(ctx *gin.Context) {
	var req ConfigUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 转换为业务DTO
	updateDto := c.convertToConfigUpdateDto(&req)

	// 更新配置
	config, err := c.repo.UpdateConfig(updateDto)
	if err != nil {
		c.log.Error("更新配置失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}
	// 清除所有旧的配置缓存
	if _, err := c.redisRepo.DelByPattern(ctx.Request.Context(), "chongli:config_cache:*"); err != nil {
		c.log.Error("清理配置缓存失败: %v", err)
	}

	// 转换为对外响应格式
	response := c.convertToConfigResponse(config)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    response,
	})
}

// ConfigDelete 删除配置（软删除）
func (c *ConfigController) ConfigDelete(ctx *gin.Context) {
	var req struct {
		Id int64 `json:"id" binding:"required,min=1"` // 主键
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 检查配置是否存在
	config, err := c.repo.GetConfigByID(req.Id)
	if err != nil {
		c.log.Error("获取配置信息失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if config == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    404,
			"message": "配置不存在",
		})
		return
	}

	// 执行软删除
	err = c.repo.DeleteConfig(req.Id)
	if err != nil {
		c.log.Error("删除配置失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}
