package dao

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"

	"gorm.io/gorm"
)

// TemplateAIRepoImpl AI模板仓库实现
type TemplateAIRepoImpl struct {
	db *gorm.DB
}

// NewTemplateAIRepo 创建AI模板仓库实例
func NewTemplateAIRepo(component *component.BootStrap) repo.TemplateAIRepo {
	return &TemplateAIRepoImpl{
		db: component.Driver.GetMysqlDb(),
	}
}

// Create 创建AI模板
func (r *TemplateAIRepoImpl) Create(_ context.Context, template *dto.TemplateAIDTO, tx ...*gorm.DB) error {
	db := r.getDB(tx...)

	// 将DTO转换为Model
	aiTemplate := r.dtoToModel(template)

	// 创建记录
	result := db.Create(aiTemplate)
	if result.Error != nil {
		return fmt.Errorf("创建AI模板失败: %w", result.Error)
	}

	// 更新DTO的ID
	template.ID = aiTemplate.ID
	return nil
}

// Update 更新AI模板
func (r *TemplateAIRepoImpl) Update(_ context.Context, id uint64, updates map[string]interface{}, tx ...*gorm.DB) error {
	db := r.getDB(tx...)

	// 执行更新
	result := db.Model(&model.AITemplate{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("更新AI模板失败: %w", result.Error)
	}

	// 检查是否找到记录
	if result.RowsAffected == 0 {
		return fmt.Errorf("未找到ID为%d的AI模板", id)
	}

	return nil
}

// Delete 删除AI模板
func (r *TemplateAIRepoImpl) Delete(_ context.Context, id uint64, tx ...*gorm.DB) error {
	db := r.getDB(tx...)

	// 执行删除
	result := db.Delete(&model.AITemplate{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除AI模板失败: %w", result.Error)
	}

	// 检查是否找到记录
	if result.RowsAffected == 0 {
		return fmt.Errorf("未找到ID为%d的AI模板", id)
	}

	return nil
}

// GetByID 根据ID获取AI模板，支持联表查询模板分类
func (r *TemplateAIRepoImpl) GetByID(_ context.Context, id uint64, tx ...*gorm.DB) (*dto.TemplateAIDTO, error) {
	db := r.getDB(tx...)

	var template model.AITemplate
	result := db.Preload("TemplateCategory").Where("id = ?", id).First(&template)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("未找到ID为%d的AI模板", id)
		}
		return nil, fmt.Errorf("查询AI模板失败: %w", result.Error)
	}

	return r.modelToDTO(&template), nil
}

// GetByQuery 根据条件查询单个AI模板，支持联表查询模板分类
func (r *TemplateAIRepoImpl) GetByQuery(_ context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) (*dto.TemplateAIQueryDTO, error) {
	db := r.getDB(tx...)

	// 构建查询
	db = r.buildQuery(db, query)

	var template model.AITemplate
	result := db.First(&template)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // 未找到记录，返回nil
		}
		return nil, fmt.Errorf("查询AI模板失败: %w", result.Error)
	}

	// 将查询结果转换为DTO
	templateDTO := r.modelToDTO(&template)

	// 构建返回结果
	resultQuery := &dto.TemplateAIQueryDTO{
		Template:       *templateDTO,
		PageNum:        query.PageNum,
		PageSize:       query.PageSize,
		OrderBy:        query.OrderBy,
		OrderDirection: query.OrderDirection,
	}

	// 如果有关联的模板分类，也设置到结果中
	if template.TemplateCategory != nil {
		resultQuery.Category = r.categoryToDTO(template.TemplateCategory)
	}

	return resultQuery, nil
}

// buildQueryConditions 构建共用的查询条件
func (r *TemplateAIRepoImpl) buildQueryConditions(db *gorm.DB, query *dto.TemplateAIQueryDTO) *gorm.DB {
	// 预加载模板分类
	db = db.Preload("TemplateCategory")

	// 添加AI模板的查询条件
	if query.Template.ID > 0 {
		db = db.Where("template_ai.id = ?", query.Template.ID)
	}
	if query.Template.Name != "" {
		db = db.Where("template_ai.name LIKE ?", "%"+query.Template.Name+"%")
	}
	if query.Template.CategoryID > 0 {
		db = db.Where("template_ai.category_id = ?", query.Template.CategoryID)
	}
	if query.Template.MainClass != 0 {
		db = db.Where("template_ai.main_class = ?", query.Template.MainClass)
	}
	if query.Template.Status != 0 {
		db = db.Where("template_ai.status = ?", query.Template.Status)
	}
	if query.Template.MaxVersionInt > 0 {
		db = db.Where("template_ai.max_version_int >= ?", query.Template.MaxVersionInt)
	}

	// 构建连接查询
	db = db.Joins("LEFT JOIN template_category ON template_ai.category_id = template_category.id")

	// 添加模板分类的查询条件
	if query.Category.ID > 0 {
		db = db.Where("template_category.id = ?", query.Category.ID)
	}
	if query.Category.IsActive != 0 {
		db = db.Where("template_category.is_active = ?", query.Category.IsActive)
	}
	if query.Category.IsDelete != 0 {
		db = db.Where("template_category.is_delete = ?", query.Category.IsDelete)
	}
	if query.Category.MainClass != 0 {
		db = db.Where("template_category.main_class = ?", query.Category.MainClass)
	}

	return db
}

// ListByQuery 根据条件查询AI模板列表，支持联表查询模板分类
func (r *TemplateAIRepoImpl) ListByQuery(_ context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateAIDTO, error) {
	db := r.getDB(tx...)

	// 构建查询条件
	db = r.buildQueryConditions(db, query)

	// 分页处理
	var templates []model.AITemplate
	if query.PageNum > 0 && query.PageSize > 0 {
		offset := (query.PageNum - 1) * query.PageSize
		db = db.Offset(offset).Limit(query.PageSize)
	}

	// 添加排序
	if query.OrderBy != "" {
		direction := "ASC"
		if query.OrderDirection == "desc" {
			direction = "DESC"
		}

		db = db.Order(fmt.Sprintf("%s %s", query.OrderBy, direction))
	} else {
		// 默认按ID降序排序
		db = db.Order("template_ai.id DESC")
	}

	// 执行查询
	err := db.Find(&templates).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("查询AI模板列表失败: %w", err)
	}

	// 转换为DTO
	dtos := make([]*dto.TemplateAIDTO, len(templates))
	for i, template := range templates {
		templateDTO := r.modelToDTO(&template)
		dtos[i] = templateDTO

		// 如果有关联的模板分类，也设置到结果中
		if template.TemplateCategory != nil {
			dtos[i].Category = r.categoryToDTO(template.TemplateCategory)
		}
	}

	return dtos, nil
}

// ListAll 查询所有AI模板，支持联表查询模板分类
func (r *TemplateAIRepoImpl) ListAll(_ context.Context, tx ...*gorm.DB) ([]*dto.TemplateAIDTO, error) {
	db := r.getDB(tx...)

	var templates []model.AITemplate
	result := db.Preload("TemplateCategory").Find(&templates)
	if result.Error != nil {
		return nil, fmt.Errorf("查询所有AI模板失败: %w", result.Error)
	}

	// 转换为DTO
	dtos := make([]*dto.TemplateAIDTO, len(templates))
	for i, template := range templates {
		dtos[i] = r.modelToDTO(&template)
	}

	return dtos, nil
}

// Count 根据条件统计AI模板数量
func (r *TemplateAIRepoImpl) Count(_ context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) (int64, error) {
	db := r.getDB(tx...)

	// 构建查询条件
	db = r.buildQueryConditions(db, query)

	// 执行统计查询
	var count int64
	if err := db.Model(&model.AITemplate{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("统计AI模板数量失败: %w", err)
	}

	return count, nil
}

// BatchUpdate 批量更新AI模板
func (r *TemplateAIRepoImpl) BatchUpdate(_ context.Context, ids []uint64, updates map[string]interface{}, tx ...*gorm.DB) error {
	db := r.getDB(tx...)

	// 验证ID列表不能为空
	if len(ids) == 0 {
		return fmt.Errorf("模板ID列表不能为空")
	}

	// 执行批量更新
	result := db.Model(&model.AITemplate{}).Where("id IN ?", ids).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("批量更新AI模板失败: %w", result.Error)
	}

	// 检查是否有记录被更新
	if result.RowsAffected == 0 {
		return fmt.Errorf("未找到要更新的AI模板记录")
	}

	return nil
}

// buildQuery 构建查询条件
func (r *TemplateAIRepoImpl) buildQuery(db *gorm.DB, query *dto.TemplateAIQueryDTO) *gorm.DB {
	// 预加载模板分类
	db = db.Preload("TemplateCategory")

	// 添加AI模板的查询条件
	if query.Template.ID > 0 {
		db = db.Where("ai_template.id = ?", query.Template.ID)
	}
	if query.Template.Name != "" {
		db = db.Where("ai_template.name LIKE ?", "%"+query.Template.Name+"%")
	}
	if query.Template.CategoryID > 0 {
		db = db.Where("ai_template.category = ?", query.Template.CategoryID)
	}
	if query.Template.Status != 0 {
		db = db.Where("ai_template.status = ?", query.Template.Status)
	}
	if query.Template.MainClass != 0 {
		db = db.Where("ai_template.main_class = ?", query.Template.MainClass)
	}
	if query.Template.MaxVersion != "" {
		db = db.Where("ai_template.max_version = ?", query.Template.MaxVersion)
	}
	if query.Template.MaxVersionInt > 0 {
		db = db.Where("ai_template.max_version_int >= ?", query.Template.MaxVersionInt)
	}

	// 添加模板分类的查询条件（联表查询）
	db = db.Joins("LEFT JOIN template_category ON ai_template.category = template_category.id")

	if query.Category.ID > 0 {
		db = db.Where("template_category.id = ?", query.Category.ID)
	}
	if query.Category.IsActive != 0 {
		db = db.Where("template_category.is_active = ?", query.Category.IsActive)
	}
	if query.Category.IsDelete != 0 {
		db = db.Where("template_category.is_delete = ?", query.Category.IsDelete)
	}
	if query.Category.MainClass != 0 {
		db = db.Where("template_category.main_class = ?", query.Category.MainClass)
	}

	return db
}

// modelToDTO 将模型转换为DTO
func (r *TemplateAIRepoImpl) modelToDTO(model *model.AITemplate) *dto.TemplateAIDTO {
	if model == nil {
		return nil
	}

	if model.VariablesJSON == "" {
		model.VariablesJSON = "{}"
	}

	// 创建DTO并设置基本字段
	result := &dto.TemplateAIDTO{
		ID:            model.ID,
		Name:          model.Name,
		CoverURL:      model.CoverURL,
		VideoCoverURL: model.VideoCoverURL,
		Status:        model.Status,
		SortOrder:     model.SortOrder,
		MaxVersionInt: model.MaxVersionInt,
		MaxVersion:    model.MaxVersion,
		CategoryID:    model.CategoryID,
		MainClass:     model.MainClass,
		Description:   model.Description,
		CreateAt:      model.CreateAt,
		UpdatedAt:     model.UpdatedAt,
		DiamondCost:   model.DiamondCost,
	}

	// 处理VariablesJSON - 将string转换为JSONMap
	if model.VariablesJSON != "" {
		var variablesMap dto.JSONMap
		if err := json.Unmarshal([]byte(model.VariablesJSON), &variablesMap); err == nil {
			result.VariablesJSON = variablesMap
		} else {
			// 如果解析失败，设置为空map
			result.VariablesJSON = make(dto.JSONMap)
		}
	} else {
		result.VariablesJSON = make(dto.JSONMap)
	}

	// 处理分类
	if model.TemplateCategory != nil {
		result.Category = r.categoryToDTO(model.TemplateCategory)
	}

	return result
}

// categoryToDTO 将模板分类模型转换为DTO
func (r *TemplateAIRepoImpl) categoryToDTO(category *model.TemplateCategory) dto.TemplateCategoryDto {
	if category == nil {
		return dto.TemplateCategoryDto{}
	}

	return dto.TemplateCategoryDto{
		ID:            category.ID,
		Name:          category.Name,
		Sort:          category.Sort,
		MaxVersion:    category.MaxVersion,
		MaxVersionInt: category.MaxVersionInt,
		IsActive:      category.IsActive,
		IsDelete:      category.IsDelete,
		CreateAt:      category.CreateAt,
		UpdateAt:      category.UpdateAt,
		MainClass:     category.MainClass,
	}
}

// dtoToModel 将DTO转换为模型
func (r *TemplateAIRepoImpl) dtoToModel(templateDTO *dto.TemplateAIDTO) *model.AITemplate {
	if templateDTO == nil {
		return nil
	}

	result := &model.AITemplate{
		ID:            templateDTO.ID,
		Name:          templateDTO.Name,
		CoverURL:      templateDTO.CoverURL,
		VideoCoverURL: templateDTO.VideoCoverURL,
		Status:        templateDTO.Status,
		MaxVersionInt: templateDTO.MaxVersionInt,
		SortOrder:     templateDTO.SortOrder,
		CategoryID:    templateDTO.CategoryID,
		MaxVersion:    templateDTO.MaxVersion,
		MainClass:     templateDTO.MainClass,
		Description:   templateDTO.Description,
		CreateAt:      templateDTO.CreateAt,
		UpdatedAt:     templateDTO.UpdatedAt,
		DiamondCost:   templateDTO.DiamondCost,
	}
	// 处理VariablesJSON - 将JSONMap转换为string
	if len(templateDTO.VariablesJSON) > 0 {
		variablesBytes, err := json.Marshal(templateDTO.VariablesJSON)
		if err == nil {
			result.VariablesJSON = string(variablesBytes)
		}
	}
	return result
}

// getDB 获取数据库连接
func (r *TemplateAIRepoImpl) getDB(tx ...*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return r.db
}
