-- 后台管理用户表
CREATE TABLE `admin_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `email` varchar(255) NOT NULL COMMENT '邮箱地址（登录凭证）',
  `nickname` varchar(100) NOT NULL COMMENT '用户昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像地址',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `login_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '登录次数',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新者ID',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已被删除，0：未删除；1：已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_create_at` (`create_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理用户表';

-- 邮箱验证码表
CREATE TABLE `email_verify_code` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `email` varchar(255) NOT NULL COMMENT '邮箱地址',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `purpose` tinyint(4) NOT NULL COMMENT '用途：1-登录，2-重置密码，3-其他',
  `expired_at` datetime NOT NULL COMMENT '过期时间',
  `is_used` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已使用：0-未使用，1-已使用',
  `client_ip` varchar(45) DEFAULT NULL COMMENT '客户端IP',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_email_code` (`email`, `code`),
  KEY `idx_expired_at` (`expired_at`),
  KEY `idx_purpose` (`purpose`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱验证码表';

-- 后台登录日志表
CREATE TABLE `admin_login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `email` varchar(255) NOT NULL COMMENT '登录邮箱',
  `login_ip` varchar(45) DEFAULT NULL COMMENT '登录IP',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `status` tinyint(4) NOT NULL COMMENT '登录状态：0-失败，1-成功',
  `message` varchar(500) DEFAULT NULL COMMENT '登录消息',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_create_at` (`create_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台登录日志表';

-- 插入默认的超级管理员用户
INSERT INTO `admin_user` (`email`, `nickname`, `created_by`) VALUES
('<EMAIL>', '超级管理员', 0); 