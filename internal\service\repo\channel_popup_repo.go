package repo

import "chongli/internal/service/dto"

// ChannelPopupRepo 渠道弹窗关联仓库接口
type ChannelPopupRepo interface {
	// CreateChannelPopup 创建渠道弹窗关联
	CreateChannelPopup(relation *dto.ChannelPopupCreateDto) (*dto.ChannelPopupDto, error)

	// GetExistingRelations 批量查询已存在的关联关系
	GetExistingRelations(channelIDs, popupIDs []int) ([]*dto.ChannelPopupDto, error)

	// BatchCreateRelations 批量创建关联关系（不检查重复）
	BatchCreateRelations(relations []dto.ChannelPopupCreateDto) ([]*dto.ChannelPopupDto, error)

	// DeleteChannelPopup 删除渠道弹窗关联（硬删除）
	DeleteChannelPopup(id int) error

	// DeleteChannelPopupByIds 批量删除渠道弹窗关联（硬删除）
	DeleteChannelPopupByIds(ids []int) error

	// DeleteChannelPopupByChannelAndPopup 根据渠道ID和弹窗ID删除关联
	DeleteChannelPopupByChannelAndPopup(channelID, popupID int) error

	// GetChannelPopupWithDetails 获取带详细信息的渠道弹窗关联列表（通用查询）
	GetChannelPopupWithDetails(query *dto.ChannelPopupQueryDto) ([]*dto.ChannelPopupDto, error)

	// GetChannelPopupCount 获取渠道弹窗关联总数（三表连查）
	GetChannelPopupCount(query *dto.ChannelPopupQueryDto) (int64, error)
}
