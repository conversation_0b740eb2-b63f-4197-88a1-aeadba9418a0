package random

import (
	uuid "github.com/satori/go.uuid"
	"math/rand"
	"strings"
	"time"
)

// GenerateRandomInt 生成0到某个区间的随机数
func GenerateRandomInt(round int) (rd int) {
	if round <= 0 {
		rd = 1
		return
	}
	rd = rand.Intn(round)
	if rd <= 0 {
		rd = 1
	}
	return
}

// CreateUUid uuid
func CreateUUid() string {
	return strings.Replace(uuid.NewV4().String(), "-", "", -1)
}

// GetRandomString 生成随机字符串
func GetRandomString(l int) string {
	str := "0123456789abcdefghijklmnopqrstuvwxyz"
	bytes := []byte(str)
	var result []byte
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}
