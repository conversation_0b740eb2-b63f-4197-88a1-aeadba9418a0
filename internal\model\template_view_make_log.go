package model

// TemplateViewMakeLog 模板浏览/制作日志
type TemplateViewMakeLog struct {
	ID         int64 `json:"id" gorm:"column:id;primaryKey;autoIncrement"`             // 主键id
	TemplateID int64 `json:"template_id" gorm:"column:template_id;default:0;not null"` // 模板id
	Type       int   `json:"type" gorm:"column:type;default:0;not null"`               // 类型：1-浏览，2-制作
	CreateAt   int64 `json:"create_at" gorm:"column:create_at;default:0;not null"`     // 创建时间
}

func (*TemplateViewMakeLog) TableName() string {
	return "template_view_make_log"
}

const (
	ViewTemplateType = iota + 1
	MakeTemplateType
)
