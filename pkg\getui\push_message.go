package getui

import (
	"chongli/component/apollo"
	"chongli/pkg/httpclient"
	"chongli/pkg/logger"
	"encoding/json"
	"time"
)

var (
	token      string
	expireTime string
)

type PushMsgRequest struct {
	RequestId   string      `json:"request_id"` // 请求唯一标识号，10-32位之间；如果request_id重复，会导致消息丢失
	Audience    audience    `json:"audience"`
	Settings    settings    `json:"settings"`
	PushMessage pushMessage `json:"push_message"`
}

// 推送目标用户
type audience struct {
	Cid []string `json:"cid"` // cid数组，只能填一个cid
}

// 推送条件设置
type settings struct {
	TTL int `json:"ttl"` // 消息离线时间设置，单位毫秒，-1表示不设离线，-1 ～ 3 * 24 * 3600 * 1000(3天)之间
}

// 在线个推通道消息内容
// 说明：厂商不支持定时展示效果，传递duration参数将无法下发厂商消息。
type pushMessage struct {
	Transmission string `json:"transmission"` // 纯透传消息内容，安卓、鸿蒙和iOS均支持，与notification、revoke 三选一，都填写时报错，长度 ≤ 3072字
}

type PushMsgResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data"`
}

func PushMsgByCid(requestId, cid string, tss *Transmission, ttl int) (any, error) {
	// 打印日志
	logger.Log().Info("Push message by cid: %s, requestId: %s, message type: %s", cid, requestId, tss.Type)

	url := baseUrl + apollo.GetApolloConfig().GetuiAppId + "/push/single/cid"
	token, expireTime, _ = auth()
	jsonMsg, err := json.Marshal(tss)
	if err != nil {
		return "", err
	}
	reqBody, err := json.Marshal(PushMsgRequest{
		RequestId: requestId,
		Settings:  settings{TTL: ttl},
		Audience:  audience{Cid: []string{cid}},
		PushMessage: pushMessage{
			Transmission: string(jsonMsg),
		},
	})
	if err != nil {
		return "", err
	}
	resp, err := httpclient.HttpPost(url, "", string(reqBody), map[string]string{"token": token, "Content-Type": "application/json;charset=utf-8"}, 10*time.Second)
	if err != nil {
		return "", err
	}
	var respData PushMsgResponse
	if err := json.Unmarshal([]byte(resp), &respData); err != nil {
		return "", err
	}
	if respData.Code == 10001 {
		token, expireTime, _ = auth()
	}
	return respData.Data, nil
}
