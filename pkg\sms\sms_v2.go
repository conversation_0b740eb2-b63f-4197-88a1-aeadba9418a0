package sms

import (
	"chongli/component/apollo"
	"chongli/pkg/httpclient"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"github.com/tidwall/gjson"
	"strings"
	"time"
)

const (
	requestUrlV2 = "https://sms.51wnl-cq.com/api/v2/mid/sms"
)

// SendParamsV2 发送验证码请求参数1
type SendParamsV2 struct {
	Uuid           string         `json:"uuid"`
	Scope          int            `json:"scope"`
	PhoneNumbers   string         `json:"phone_numbers"`
	TemplateParams TemplateParams `json:"template_params"`
}

// TemplateParams 发送验证码请求参数2
type TemplateParams struct {
	Code string `json:"code"`
}

// SendSmsV2 发送验证码
func SendSmsV2(mobile string, code string) error {
	body, err := json.Marshal(SendParamsV2{
		Uuid:         apollo.GetApolloConfig().SmsUUID,
		Scope:        1,
		PhoneNumbers: mobile,
		TemplateParams: TemplateParams{
			Code: code,
		},
	})
	if err != nil {
		return err
	}
	resp, err := httpclient.HttpPost(requestUrlV2, "", string(body), map[string]string{"Authorization": getSignV2(mobile)}, 10*time.Second)
	if err != nil {
		return err
	}
	respCode := gjson.Get(resp, "Code").String()
	if respCode != "OK" {
		return errors.New(gjson.Get(resp, "Message").String())
	}
	return nil
}

// 加密获取签名
func getSignV2(phoneNumbers string) string {
	md5Byte := []byte(apollo.GetApolloConfig().SmsUUID + `:` + fmt.Sprintf("%v", 1) + `:` + fmt.Sprintf("%v", phoneNumbers))
	return `sms ` + strings.ToUpper(fmt.Sprintf("%x", md5.Sum(md5Byte)))
}
