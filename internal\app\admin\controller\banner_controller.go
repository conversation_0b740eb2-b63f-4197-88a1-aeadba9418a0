package controller

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BannerController struct {
	log        *logger.Logger
	tx         driver.ITransaction
	bannerRepo repo.BannerRepo
}

func NewBannerController(
	bootStrap *component.BootStrap,
	bannerRepo repo.BannerRepo,
) *BannerController {
	return &BannerController{
		log:        bootStrap.Log,
		tx:         bootStrap.Tx,
		bannerRepo: bannerRepo,
	}
}

// GetBannerList 分页条件查询Banner
func (c *BannerController) GetBannerList(ctx *gin.Context) {
	var req dto.BannerPageRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	resp, err := c.bannerRepo.PageBanner(&req)
	if err != nil {
		c.log.Error("分页查询Banner失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, resp, nil, response.WithSLSLog)
}

// GetBannerDetail 获取Banner详情
func (c *BannerController) GetBannerDetail(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("ID参数错误"), response.WithSLSLog)
		return
	}

	banner, err := c.bannerRepo.GetBannerById(id)
	if err != nil {
		c.log.Error("查询Banner详情失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if banner == nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("Banner不存在"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, banner, nil, response.WithSLSLog)
}

// CreateBanner 创建Banner
func (c *BannerController) CreateBanner(ctx *gin.Context) {
	var req dto.CreateBannerRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	// 检查Banner标题是否已存在
	existBanner, err := c.bannerRepo.GetBannerByTitle(req.Title)
	if err != nil {
		c.log.Error("检查Banner标题失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	if existBanner != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("Banner标题已存在"), response.WithSLSLog)
		return
	}

	mysqlTx := c.tx.MysqlDbTxBegin()
	banner, err := c.bannerRepo.CreateBanner(&req, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("创建Banner失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, banner, nil, response.WithSLSLog)
}

// UpdateBanner 更新Banner
func (c *BannerController) UpdateBanner(ctx *gin.Context) {
	var req dto.UpdateBannerRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	// 检查Banner是否存在
	existBanner, err := c.bannerRepo.GetBannerById(req.ID)
	if err != nil {
		c.log.Error("查询Banner失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	if existBanner == nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("Banner不存在"), response.WithSLSLog)
		return
	}

	// 如果修改了标题，检查新标题是否已存在
	if req.Title != existBanner.Title {
		titleBanner, err := c.bannerRepo.GetBannerByTitle(req.Title)
		if err != nil {
			c.log.Error("检查Banner标题失败: %v", err)
			response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
			return
		}
		if titleBanner != nil && titleBanner.ID != req.ID {
			response.Response(ctx, nil, nil, errpkg.NewLowError("Banner标题已存在"), response.WithSLSLog)
			return
		}
	}

	mysqlTx := c.tx.MysqlDbTxBegin()
	err = c.bannerRepo.UpdateBanner(&req, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("更新Banner失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

// BatchUpdateBanner 批量更新Banner
func (c *BannerController) BatchUpdateBanner(ctx *gin.Context) {
	var req dto.BatchUpdateBannerRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	mysqlTx := c.tx.MysqlDbTxBegin()
	err := c.bannerRepo.BatchUpdateBanner(&req, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("批量更新Banner失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}
