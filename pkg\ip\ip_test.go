package ip

import (
	"strings"
	"testing"
)

func TestIp2Long(t *testing.T) {
	conv := Ip2Long("127.0.0.1")
	if conv == 0 {
		t.<PERSON><PERSON><PERSON>("Ip2Long unit test fail")
		return
	}
	Ip2Long("1")
}

func BenchmarkIp2Long(b *testing.B) {
	b.<PERSON>()
	for i := 0; i < b.N; i++ {
		Ip2Long("127.0.0.1")
	}
}

func TestLong2Ip(t *testing.T) {
	conv := Long2Ip(2130706433)
	if conv != "127.0.0.1" {
		t.Error("Long2Ip unit test fail")
		return
	}
}

func BenchmarkLong2Ip(b *testing.B) {
	b.<PERSON>()
	for i := 0; i < b.N; i++ {
		Long2Ip(2130706433)
	}
}

func TestGetLocationByIP(t *testing.T) {
	location, err := GetLocationByIP("**************")
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(location)
}

func TestIp2Location(t *testing.T) {
	location, err := GetLocationByIP("**************")
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(location)
}

func TestGetIPLocationV2(t *testing.T) {
	// 测试正常IP地址
	location, err := GetIPLocationV2("**************")
	if err != nil {
		t.Logf("GetIPLocationV2 error: %v", err)
		// 由于这是外部API，可能会失败，所以不直接报错
		return
	}
	t.Logf("GetIPLocationV2 result: %s", location)
}

func TestGetIPLocationV2_EmptyIP(t *testing.T) {
	// 测试空IP地址
	_, err := GetIPLocationV2("")
	if err == nil {
		t.Error("Expected error for empty IP, but got nil")
	}
	t.Logf("Expected error: %v", err)
}

func TestMd5Lower(t *testing.T) {
	// 测试md5Lower函数
	ip := "**************"
	result := md5Lower(ip)
	if result == "" {
		t.Error("md5Lower returned empty string")
	}
	// 验证结果是小写的
	if result != strings.ToLower(result) {
		t.Error("md5Lower result is not lowercase")
	}
	t.Logf("md5Lower(%s) = %s", ip, result)
}
