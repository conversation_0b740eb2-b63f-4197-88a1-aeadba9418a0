package zlib

import (
	"bytes"
	"compress/zlib"
	"io"
)

// DoZlibCompress zlib压缩字符串.
func DoZlibCompress(src []byte) (compress []byte, err error) {
	var in bytes.Buffer
	defer in.Reset()
	w := zlib.NewWriter(&in)
	_, err = w.Write(src)
	if err != nil {
		return
	}
	err = w.Close()
	if err != nil {
		return
	}
	compress = in.Bytes()
	return
}

// DoZlibUnCompress 解压zlib字符串.
func DoZlibUnCompress(compressSrc []byte) (unCompress []byte, err error) {
	b := bytes.NewReader(compressSrc)
	var out bytes.Buffer
	defer out.Reset()
	r, _ := zlib.NewReader(b)
	_, err = io.Copy(&out, r)
	if err != nil {
		return
	}
	unCompress = out.Bytes()
	return
}
