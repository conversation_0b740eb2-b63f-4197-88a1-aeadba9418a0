package repo

import (
	"chongli/internal/service/dto"
	"gorm.io/gorm"
)

// BannerRepo Banner数据仓库接口
type BannerRepo interface {
	// CreateBanner 创建Banner
	CreateBanner(req *dto.CreateBannerRequest, tx ...*gorm.DB) (*dto.BannerDto, error)
	// GetBannerById 根据ID查询Banner
	GetBannerById(id int64, tx ...*gorm.DB) (*dto.BannerDto, error)
	// UpdateBanner 更新Banner
	UpdateBanner(req *dto.UpdateBannerRequest, tx ...*gorm.DB) error
	// DeleteBanner 删除Banner（软删除）
	DeleteBanner(id int64, tx ...*gorm.DB) error
	// GetBannerByTitle 根据标题查询Banner
	GetBannerByTitle(title string, tx ...*gorm.DB) (*dto.BannerDto, error)
	// PageBanner 分页查询Banner
	PageBanner(req *dto.BannerPageRequest) (*dto.BannerPageResponse, error)
	// UpdateBannerStatus 更新Banner状态
	UpdateBannerStatus(id int64, isActive int8, tx ...*gorm.DB) error
	// BatchDeleteBanner 批量删除Banner
	BatchDeleteBanner(ids []int64, tx ...*gorm.DB) error
	// GetAllActiveBanners 获取所有启用的Banner
	GetAllActiveBanners(tx ...*gorm.DB) ([]*dto.BannerDto, error)
	// ListBanner 获取Banner列表（前端API用）
	ListBanner(version, location string) ([]*dto.BannerDto, error)
	// BatchUpdateBanner 批量更新Banner
	BatchUpdateBanner(req *dto.BatchUpdateBannerRequest, tx ...*gorm.DB) error
}
