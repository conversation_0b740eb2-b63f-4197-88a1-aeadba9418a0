package model

import (
	"time"
)

// PayOrder 支付订单
type PayOrder struct {
	ID                  int64      `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                                       // 主键id
	OrderID             string     `json:"order_id" gorm:"column:order_id;uniqueIndex;not null"`                               // 订单id
	WnlOrderID          string     `json:"wnl_order_id" gorm:"column:wnl_order_id;default:''"`                                 // 万年历支付成功订单id
	OrderState          int        `json:"order_state" gorm:"column:order_state;default:-1;not null"`                          // 订单状态，-1：待支付；1：已支付；2：已过期；3：已退款
	GoodsID             int64      `json:"goods_id" gorm:"column:goods_id;not null"`                                           // 商品主键id
	GoodsMiddleID       string     `json:"goods_middle_id" gorm:"column:goods_middle_id;not null"`                             // 商品中台id
	GoodsTitle          string     `json:"goods_title" gorm:"column:goods_title;default:''"`                                   // 商品名称
	UserID              int64      `json:"user_id" gorm:"column:user_id;not null"`                                             // 用户id
	UserDeviceID        string     `json:"user_device_id" gorm:"column:user_device_id;default:''"`                             // 用户设备id
	UserBindChannelID   int64      `json:"user_bind_channel_id" gorm:"column:user_bind_channel_id;default:0"`                  // 用户绑定渠道id
	UserBindChannelName string     `json:"user_bind_channel_name" gorm:"column:user_bind_channel_name;default:''"`             // 用户绑定渠道名称
	UserIP              string     `json:"user_ip" gorm:"column:user_ip;default:''"`                                           // 用户ip
	PayType             int        `json:"pay_type" gorm:"column:pay_type;default:-1;not null"`                                // 支付方式，1：微信；2：支付宝；3：苹果支付；4：沙盒支付
	OrderAmount         float64    `json:"order_amount" gorm:"column:order_amount;type:decimal(10,2);default:0.00;not null"`   // 订单应付金额
	PayAmount           float64    `json:"pay_amount" gorm:"column:pay_amount;type:decimal(10,2);default:0.00;not null"`       // 实际支付金额
	RefundAmount        float64    `json:"refund_amount" gorm:"column:refund_amount;type:decimal(10,2);default:0.00;not null"` // 退款金额
	Version             string     `json:"version" gorm:"column:version;not null"`                                             // app版本
	Channel             string     `json:"channel" gorm:"column:channel;not null"`                                             // app渠道
	PayAt               *time.Time `json:"pay_at" gorm:"column:pay_at"`                                                        // 支付时间
	RefundAt            *time.Time `json:"refund_at" gorm:"column:refund_at"`                                                  // 退款时间
	ExpireAt            *time.Time `json:"expire_at" gorm:"column:expire_at"`                                                  // 订单过期时间
	CreateAt            time.Time  `json:"create_at" gorm:"column:create_at;autoCreateTime"`                                   // 创建时间
	UpdateAt            time.Time  `json:"update_at" gorm:"column:update_at;autoUpdateTime"`                                   // 更新时间
	WnlCallbackData     string     `json:"wnl_callback_data" gorm:"column:wnl_callback_data;type:text"`                        // 万年历支付回调数据
	Remark              string     `json:"remark" gorm:"column:remark;default:''"`                                             // 订单备注
	IsDelete            int8       `json:"is_delete" gorm:"column:is_delete;default:-1;not null"`                              // 是否已被删除，-1：未删除；1：已删除
}

// TableName 表名称
func (*PayOrder) TableName() string {
	return "pay_order"
}

// 订单状态常量
const (
	OrderStatePending = -1 // 待支付
	OrderStatePaid    = 1  // 已支付
	OrderStateExpired = 2  // 已过期
	OrderStateRefund  = 3  // 已退款
)

// 支付方式常量
const (
	PayTypeWechat  = 1 // 微信
	PayTypeAlipay  = 2 // 支付宝
	PayTypeApple   = 3 // 苹果支付
	PayTypeSandbox = 4 // 沙盒支付
)

// 删除状态常量
const (
	NotDeleted = -1 // 未删除
	Deleted    = 1  // 已删除
)

// GetOrderStateText 获取订单状态文本
func (p *PayOrder) GetOrderStateText() string {
	switch p.OrderState {
	case OrderStatePending:
		return "待支付"
	case OrderStatePaid:
		return "已支付"
	case OrderStateExpired:
		return "已过期"
	case OrderStateRefund:
		return "已退款"
	default:
		return "未知状态"
	}
}

// GetPayTypeText 获取支付方式文本
func (p *PayOrder) GetPayTypeText() string {
	switch p.PayType {
	case -1:
		return "未支付"
	case PayTypeWechat:
		return "微信支付"
	case PayTypeAlipay:
		return "支付宝"
	case PayTypeApple:
		return "苹果支付"
	case PayTypeSandbox:
		return "沙盒支付"
	default:
		return "未知支付方式"
	}
}

// IsPaid 是否已支付
func (p *PayOrder) IsPaid() bool {
	return p.OrderState == OrderStatePaid
}

// IsExpired 是否已过期
func (p *PayOrder) IsExpired() bool {
	return p.OrderState == OrderStateExpired || (p.ExpireAt != nil && p.ExpireAt.Before(time.Now()))
}

// IsRefunded 是否已退款
func (p *PayOrder) IsRefunded() bool {
	return p.OrderState == OrderStateRefund
}
