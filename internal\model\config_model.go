package model

import "time"

type Config struct {
	Id          int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`            // 主键id
	ConfigKey   string    `gorm:"column:config_key;NOT NULL" json:"config_key"`              // 配置名
	ConfigValue string    `gorm:"column:config_value;NOT NULL" json:"config_value"`          // 配置值
	Remark      string    `gorm:"column:remark;NOT NULL" json:"remark"`                      // 备注信息
	CreateAt    time.Time `gorm:"column:create_at;NOT NULL;autoCreateTime" json:"create_at"` // 创建时间
	IsDelete    int8      `gorm:"column:is_delete;NOT NULL;default:-1" json:"is_delete"`     // 删除标志
	IsChannel   int8      `gorm:"column:is_channel;NOT NULL;default:-1" json:"is_channel"`   // 是否是渠道配置
	IsPublish   int8      `gorm:"column:is_publish;NOT NULL;default:-1" json:"is_publish"`   // 是否发布
}

func (m *Config) TableName() string {
	return "config"
}
