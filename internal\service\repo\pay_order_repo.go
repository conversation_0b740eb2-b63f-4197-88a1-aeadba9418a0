package repo

import (
	"chongli/internal/service/dto"

	"gorm.io/gorm"
)

// PayOrderRepo 支付订单数据仓库接口
type PayOrderRepo interface {
	// CreatePayOrder 创建支付订单
	CreatePayOrder(order *dto.PayOrderDto, tx ...*gorm.DB) error

	// GetPayOrderByID 根据ID获取支付订单
	GetPayOrderByID(id int64, tx ...*gorm.DB) (*dto.PayOrderDto, error)

	// GetPayOrderByOrderID 根据订单ID获取支付订单
	GetPayOrderByOrderID(orderID string, tx ...*gorm.DB) (*dto.PayOrderDto, error)

	// GetPayOrderByWnlOrderID 根据万年历订单ID获取支付订单
	GetPayOrderByWnlOrderID(wnlOrderID string, tx ...*gorm.DB) (*dto.PayOrderDto, error)

	// UpdatePayOrder 更新支付订单
	UpdatePayOrder(order *dto.PayOrderDto, tx ...*gorm.DB) error

	// UpdatePayOrderByID 根据ID更新支付订单
	UpdatePayOrderByID(id int64, fields map[string]any, tx ...*gorm.DB) error

	// UpdatePayOrderByOrderID 根据订单ID更新支付订单
	UpdatePayOrderByOrderID(orderID string, fields map[string]any, tx ...*gorm.DB) error

	// DeletePayOrder 删除支付订单（软删除）
	DeletePayOrder(id int64, tx ...*gorm.DB) error

	// DeletePayOrderByOrderID 根据订单ID删除支付订单（软删除）
	DeletePayOrderByOrderID(orderID string, tx ...*gorm.DB) error

	// GetPayOrderList 获取支付订单列表
	GetPayOrderList(req *dto.GetPayOrderListRequest) ([]*dto.PayOrderDto, int64, error)

	// GetPayOrdersByUserID 根据用户ID获取支付订单列表
	GetPayOrdersByUserID(userID int64, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetPayOrdersByGoodsID 根据商品ID获取支付订单列表
	GetPayOrdersByGoodsID(goodsID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetPayOrdersByState 根据订单状态获取支付订单列表
	GetPayOrdersByState(state int, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetExpiredOrders 获取过期订单列表
	GetExpiredOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetPendingOrders 获取待支付订单列表
	GetPendingOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetPaidOrders 获取已支付订单列表
	GetPaidOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// CountOrdersByUserID 统计用户订单数量
	CountOrdersByUserID(userID int64, tx ...*gorm.DB) (int64, error)

	// CountOrdersByState 统计指定状态的订单数量
	CountOrdersByState(state int, tx ...*gorm.DB) (int64, error)

	// CountOrdersByPayType 统计指定支付方式的订单数量
	CountOrdersByPayType(payType int, tx ...*gorm.DB) (int64, error)

	// GetOrderAmountSumByUserID 获取用户订单总金额
	GetOrderAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error)

	// GetPayAmountSumByUserID 获取用户实际支付总金额
	GetPayAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error)

	// GetRefundAmountSumByUserID 获取用户退款总金额
	GetRefundAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error)

	// BatchUpdateOrderState 批量更新订单状态
	BatchUpdateOrderState(orderIDs []string, state int, tx ...*gorm.DB) error

	// GetOrdersByChannel 根据渠道获取订单列表
	GetOrdersByChannel(channel string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetOrdersByVersion 根据版本获取订单列表
	GetOrdersByVersion(version string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetOrdersByUserIP 根据用户IP获取订单列表
	GetOrdersByUserIP(userIP string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetOrdersByUserDeviceID 根据用户设备ID获取订单列表
	GetOrdersByUserDeviceID(userDeviceID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// GetOrdersByUserBindChannelID 根据用户绑定渠道ID获取订单列表
	GetOrdersByUserBindChannelID(userBindChannelID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// IsOrderExists 检查订单是否存在
	IsOrderExists(orderID string, tx ...*gorm.DB) (bool, error)

	// IsWnlOrderExists 检查万年历订单是否存在
	IsWnlOrderExists(wnlOrderID string, tx ...*gorm.DB) (bool, error)

	// GetExpiredUnpaidOrders 获取过期未支付订单列表
	GetExpiredUnpaidOrders(tx ...*gorm.DB) ([]*dto.PayOrderDto, error)

	// BatchUpdateOrderStateAndRemark 批量更新订单状态和备注
	BatchUpdateOrderStateAndRemark(orderIDs []string, state int, remark string, tx ...*gorm.DB) error

	// 统计相关方法
	// GetOrderStatistics 获取订单统计数据
	GetOrderStatistics() (*dto.OrderStatisticsDto, error)
	// GetTotalOrdersCount 获取总订单量
	GetTotalOrdersCount() (int64, error)
	// GetTotalOrdersByChannel 获取各渠道总订单量
	GetTotalOrdersByChannel() ([]dto.ChannelOrderCountDto, error)
	// GetTotalPaidAmount 获取总已支付金额（排除沙盒）
	GetTotalPaidAmount() (float64, error)
	// GetTotalPaidAmountByChannel 获取各渠道总已支付金额（排除沙盒）
	GetTotalPaidAmountByChannel() ([]dto.ChannelOrderAmountDto, error)
	// GetTotalRefundAmount 获取总退款金额（排除沙盒）
	GetTotalRefundAmount() (float64, error)
	// GetTotalRefundAmountByChannel 获取各渠道总退款金额（排除沙盒）
	GetTotalRefundAmountByChannel() ([]dto.ChannelOrderAmountDto, error)
	// GetTotalPaidOrdersCount 获取总已支付订单量（排除沙盒）
	GetTotalPaidOrdersCount() (int64, error)
	// GetTotalPaidOrdersByChannel 获取各渠道总已支付订单量（排除沙盒）
	GetTotalPaidOrdersByChannel() ([]dto.ChannelOrderCountDto, error)
	// GetMonthlyPaidOrderStats 获取每月已支付订单统计（当前年份1月到当前月）
	GetMonthlyPaidOrderStats(year int) ([]dto.MonthlyOrderStatsDto, error)
	// GetYesterdayNewOrdersCount 获取昨日新增订单量
	GetYesterdayNewOrdersCount() (int64, error)
	// GetYesterdayNewOrdersByChannel 获取昨日各渠道新增订单
	GetYesterdayNewOrdersByChannel() ([]dto.ChannelOrderCountDto, error)
	// GetYesterdayNewPaidOrdersCount 获取昨日新增已支付订单量（排除沙盒）
	GetYesterdayNewPaidOrdersCount() (int64, error)
	// GetYesterdayNewPaidOrdersByChannel 获取昨日各渠道新增已支付订单（排除沙盒）
	GetYesterdayNewPaidOrdersByChannel() ([]dto.ChannelOrderCountDto, error)
	// GetThisMonthNewOrdersCount 获取本月新增订单量
	GetThisMonthNewOrdersCount() (int64, error)
	// GetThisMonthNewOrdersByChannel 获取本月各渠道新增订单
	GetThisMonthNewOrdersByChannel() ([]dto.ChannelOrderCountDto, error)
	// GetThisMonthNewPaidOrdersCount 获取本月新增已支付订单量（排除沙盒）
	GetThisMonthNewPaidOrdersCount() (int64, error)
	// GetThisMonthNewPaidOrdersByChannel 获取本月各渠道新增已支付订单（排除沙盒）
	GetThisMonthNewPaidOrdersByChannel() ([]dto.ChannelOrderCountDto, error)
	// GetTodayNewOrdersCount 获取本日新增订单量
	GetTodayNewOrdersCount() (int64, error)
	// GetTodayNewOrdersByChannel 获取本日各渠道新增订单
	GetTodayNewOrdersByChannel() ([]dto.ChannelOrderCountDto, error)
	// GetTodayNewPaidOrdersCount 获取本日新增已支付订单量（排除沙盒）
	GetTodayNewPaidOrdersCount() (int64, error)
	// GetTodayNewPaidOrdersByChannel 获取本日各渠道新增已支付订单（排除沙盒）
	GetTodayNewPaidOrdersByChannel() ([]dto.ChannelOrderCountDto, error)
	// GetThisMonthDailyNewPaidOrders 获取本月每日新增已支付订单统计
	GetThisMonthDailyNewPaidOrders() ([]dto.DailyOrderStatsDto, error)
}
