package stringchar

import (
	"bytes"
	"testing"
)

func TestReverse(t *testing.T) {
	s1 := ""
	r1 := Reverse(s1)
	if s1 != r1 {
		t.<PERSON><PERSON><PERSON>("The values of %v is not %v\n", r1, s1)
	}

	s2 := "abc123"
	want := "321cba"
	r2 := Reverse(s2)
	if r2 != want {
		t.<PERSON>rrorf("The values of %v is not %v\n", r2, s2)
	}
}

func TestMd5Hex(t *testing.T) {
	b1 := []byte{56, 50, 55, 99, 99, 98, 48, 101, 101, 97, 56, 97, 55, 48, 54, 99, 52, 99, 51, 52, 97, 49, 54, 56, 57, 49, 102, 56, 52, 101, 55, 98}
	hex1 := Md5Hex([]byte("12345"), 0)
	if !bytes.Equal(b1, hex1) {
		t.<PERSON><PERSON><PERSON>("The hex of %v is not %v\n", hex1, b1)
	}

	b2 := []byte{56, 50}
	hex2 := Md5Hex([]byte("12345"), 2)
	if !bytes.Equal(b2, hex2) {
		t.<PERSON><PERSON><PERSON>("The hex of %v is not %v\n", hex2, b2)
	}
}
