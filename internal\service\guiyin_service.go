package service

import (
	"chongli/component"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"

	"github.com/panjf2000/ants/v2"
)

type GuiyinService struct {
	guiyinDeviceBindRepo repo.GuiyinDeviceBindRepo
	antsPool             *ants.Pool
	log                  *logger.Logger
	payOrderRepo         repo.PayOrderRepo
}

func NewGuiyinService(bootStrap *component.BootStrap, guiyinDeviceBindRepo repo.GuiyinDeviceBindRepo) *GuiyinService {
	return &GuiyinService{
		antsPool:             bootStrap.GoroutinePool,
		log:                  bootStrap.Log,
		guiyinDeviceBindRepo: guiyinDeviceBindRepo,
	}
}

// GetChannelIDByDeviceID 通过deviceId查询渠道ID，如果不存在则确定并更新
func (g *GuiyinService) GetChannelIDByDeviceID(deviceID string, _ string) (int, error) {
	if deviceID == "" {
		return 1, errors.New("设备ID不能为空")
	}

	// 1. 首先查询设备绑定信息
	deviceBindInfo, err := g.guiyinDeviceBindRepo.GetGuiyinDeviceBindByDeviceID(deviceID)
	if err != nil {
		g.log.Error("获取设备绑定信息异常: %v", err.Error())
		return 1, err
	}

	// 2. 检查渠道ID是否存在
	if deviceBindInfo != nil && deviceBindInfo.ChannelId > 0 {
		// 渠道ID存在，直接返回
		return deviceBindInfo.ChannelId, nil
	}
	return 1, nil
}

// func (g *GuiyinService) ReportRegisterData(userData *reporter.UserData, channel string) {
// 	// 上报归因数据
// 	bindData, err := g.guiyinDeviceBindRepo.GetGuiyinDeviceBindByDeviceID(userData.DeviceId)
// 	if err != nil {
// 		g.log.Error("获取 guiyin_device_bind 归因数据失败: %v, DeviceId: %v", err, userData.DeviceId)
// 	}

// 	reportData, err := reporter.ParseReportData(bindData.AllData, channel)
// 	if err != nil {
// 		g.log.Error("解析归因数据 all_data 失败: %v", err)
// 		return
// 	}

// 	if reportData != nil {
// 		if reportData.Client == "" {
// 			g.log.Info("没有获取到归因数据, 不进行上报")
// 			return
// 		}
// 		reportData.ThirdUserID = strconv.FormatInt(userData.UserId, 10)
// 		reportData.ActionTime = time.Now().UnixMilli()
// 		reportData.IP = userData.IP
// 		if err := reporter.ReportRegister(reportData, userData); err != nil {
// 			g.log.Error("上报注册数据失败: %v", err)
// 		} else {
// 			g.log.Info("上报注册数据成功: %+v", reportData)
// 		}
// 	}
// }

// func (g *GuiyinService) ReportPaySuccessData(orderId string, payAt time.Time) {
// 	// 上报归因数据
// 	orderData, err := g.payOrderRepo.GetPayOrderByOrderID(orderId)
// 	if err != nil {
// 		logger.Log().Error("获取订单数据失败: %v, orderId: %v", err, orderId)
// 	}

// 	bindData, err := g.guiyinDeviceBindRepo.GetGuiyinDeviceBindByDeviceID(orderData.UserDeviceID)
// 	if err != nil {
// 		g.log.Error("获取 guiyin_device_bind 归因数据失败: %v, DeviceId: %v", err, orderData.UserDeviceID)
// 	}

// 	reportData, err := reporter.ParseReportData(bindData.AllData, orderData.Channel)
// 	if err != nil {
// 		g.log.Error("解析归因数据 all_data 失败: %v", err)
// 		return
// 	}

// 	if reportData != nil {
// 		if reportData.Client == "" {
// 			logger.Log().Info("没有获取到归因数据, 不进行上报")
// 			return
// 		}
// 		reportData.ThirdUserID = strconv.FormatInt(orderData.UserID, 10)
// 		reportData.ActionTime = time.Now().UnixMilli()
// 		if err := reporter.ReportPaySuccess(reportData, &reporter.OrderData{
// 			OrderID:     orderData.OrderID,
// 			OrderAmount: orderData.OrderAmount,
// 			PayAt:       payAt.UnixMilli(),
// 			UserID:      orderData.UserID,
// 		}, &reporter.GoodsData{
// 			GoodsID:   orderData.GoodsMiddleID,
// 			GoodsName: orderData.GoodsTitle,
// 		}); err != nil {
// 			logger.Log().Error("上报支付数据失败: %v", err)
// 		}
// 	}
// }
