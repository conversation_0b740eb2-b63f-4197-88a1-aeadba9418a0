package router

import (
	"chongli/internal/app/admin/controller"
	"chongli/internal/middleware"
	"github.com/gin-gonic/gin"
)

type UserManageRouter struct {
	userCtrl   *controller.UserManageController
	guiyinCtrl *controller.GuiYinController
}

// NewUserManageRouter 创建用户管理路由
func NewUserManageRouter(engine *gin.Engine, userCtrl *controller.UserManageController, guiyinCtrl *controller.GuiYinController) *UserManageRouter {
	router := &UserManageRouter{userCtrl: userCtrl, guiyinCtrl: guiyinCtrl}

	// 用户管理相关路由（需要JWT验证）
	userManage := engine.Group("/admin/user")
	userManage.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		userManage.GET("/list", router.userCtrl.UserList)         // 获取用户列表
		userManage.GET("/detail/:id", router.userCtrl.UserDetail) // 获取用户详情
		userManage.POST("/edit", router.userCtrl.UserEdit)        // 编辑用户信息
	}
	{
		userManage.POST("/diamond/add", router.userCtrl.AddUserDiamond)           // 给用户添加钻石
		userManage.POST("/diamond/sub", router.userCtrl.SubUserDiamond)           // 扣除用户钻石
		userManage.GET("/diamond_record/list", router.userCtrl.DiamondRecordList) // 获取用户钻石记录列表
	}
	{
		// 获取用户个推绑定列表
		userManage.GET("/getui/list", router.userCtrl.GetuiList)
		// 删除用户个推绑定
		userManage.DELETE("/getui/delete/:id", router.userCtrl.DeleteUserGetuiBind)
	}
	{
		userManage.GET("/vip/info", router.userCtrl.UserVipInfo)              // 获取用户VIP信息
		userManage.POST("/vip/open", router.userCtrl.OpenUserVip)             // 开通用户VIP
		userManage.POST("/vip/renew", router.userCtrl.RenewUserVip)           // 续费用户VIP
		userManage.GET("/vip/give_record", router.userCtrl.VipGiveRecordList) // VIP赠送记录
	}
	{
		userManage.GET("/guiyin/list", router.guiyinCtrl.List)         // 获取用户列表
		userManage.GET("/guiyin/delete/:id", router.guiyinCtrl.Delete) // 删除用户
	}
	{
		userManage.GET("/statistics", router.userCtrl.UserStatistics) // 获取用户统计数据
	}

	return router
}
