package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	"chongli/internal/service/dto"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"github.com/gin-gonic/gin"
)

type PayOrderController struct {
	payOrderBiz *biz.PayOrderService
	log         *logger.Logger
}

func NewPayOrderController(
	bootstrap *component.BootStrap,
	payOrderBiz *biz.PayOrderService,
) *PayOrderController {
	return &PayOrderController{
		payOrderBiz: payOrderBiz,
		log:         bootstrap.Log,
	}
}

// getRequestHeader 获取请求头信息
func (c *PayOrderController) getRequestHeader(ctx *gin.Context, req *dto.CreateOrderRequest) {
	req.Version = ctx.Request.Header.Get("version")
	req.Channel = ctx.Request.Header.Get("channel")
	req.UserDeviceId = ctx.Request.Header.Get("deviceId")
}

// CreateOrder 创建订单（下单）
func (c *PayOrderController) CreateOrder(ctx *gin.Context) {
	var req dto.CreateOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, req, nil, errpkg.NewLowError(response.BadRequest), response.WithSLSLog)
		return
	}

	// 获取请求头信息
	c.getRequestHeader(ctx, &req)

	// 获取用户ID
	req.UserId = utils.GetUserId(ctx)
	if req.UserId <= 0 {
		response.Response(ctx, req, nil, errpkg.NewLowError("用户未登录"), response.WithSLSLog)
		return
	}

	// 获取用户IP
	req.UserIP = ctx.ClientIP()

	// 调用业务逻辑
	resp, bizErr := c.payOrderBiz.CreateOrder(&req)
	if bizErr != nil {
		response.Response(ctx, req, nil, bizErr, response.WithSLSLog)
		return
	}

	response.Response(ctx, req, resp, nil, response.WithSLSLog)
}
