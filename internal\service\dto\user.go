package dto

import "time"

// UserInfoDto 用户信息数据传输对象
type UserInfoDto struct {
	ID              int64     `json:"id"`               // 主键id
	DeviceId        string    `json:"device_id"`        // 设备id
	Avatar          string    `json:"avatar"`           // 头像
	Username        string    `json:"username"`         // 用户名
	Phone           string    `json:"phone"`            // 手机号
	Diamond         uint64    `json:"diamond"`          // 钻石
	IsVip           int8      `json:"is_vip"`           // 是否是vip，-1：不是；1：是
	VipType         int8      `json:"vip_type"`         // vip类型，1：月卡；2：季卡；3：年卡
	RegisterType    int64     `json:"register_type"`    // 注册方式，1：一键登录；2：验证码；3：苹果
	RegisterVersion string    `json:"register_version"` // 注册版本
	Channel         string    `json:"channel"`          // 渠道
	Ip              string    `json:"ip"`               // IP地址
	IPLocation      string    `json:"ip_location"`      // IP地理位置
	CreateAt        time.Time `json:"create_at" `       // 创建时间
	UpdateAt        time.Time `json:"update_at" `       // 更新时间
	IsDelete        int8      `json:"is_delete" `       // 是否已被删除，0：未删除；1：已注销

	// guiyin_bind_info
	UserBindChannelId   int64  `json:"user_bind_channel_id"`   // 用户绑定渠道id
	UserBindChannelName string `json:"user_bind_channel_name"` // 用户绑定渠道名称
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	UserOneClickLoginRequest
	UserPhoneCodeLoginRequest
	Type     int    `json:"type"`     // 登录类型 1:一键登录 2:验证码登录 3:苹果登录
	Version  string `json:"version"`  // 版本号
	Channel  string `json:"channel"`  // 渠道
	DeviceId string `json:"deviceId"` // 设备号
	Ip       string `json:"ip"`       // IP地址
}

type UserOneClickLoginRequest struct {
	Token string `json:"token"`
	Gyuid string `json:"gyuid"`
}

type UserPhoneCodeLoginRequest struct {
	Code  string `json:"code"`
	Phone string `json:"phone"`
}

// UserLoginResponse 用户登录响应
type UserLoginResponse struct {
	UserInfo *UserInfo `json:"user_info"`
	Token    string    `json:"token"`
}

type UserInfo struct {
	ID       int64        `json:"id"`
	Avatar   string       `json:"avatar"`   // 头像
	Username string       `json:"username"` // 用户名
	Diamond  uint64       `json:"diamond"`  // 钻石
	Phone    string       `json:"phone"`    // 手机号
	IsVip    int8         `json:"is_vip"`   // 是否是vip，-1：不是；1：是
	VipInfo  *UserVipInfo `json:"vip_info"` // 会员信息
}

// UserSendCodeRequest 发送验证码请求
type UserSendCodeRequest struct {
	Phone     string `json:"phone" binding:"required"`
	Signature string `json:"signature"`
	Timestamp int64  `json:"timestamp"`
}

type GetUserListRequest struct {
	Page                int       `json:"page" form:"page" binding:"required"`                  // 页码
	PageSize            int       `json:"size" form:"size" binding:"required"`                  // 每页数量
	ID                  int64     `json:"id" form:"id"`                                         // 主键id
	DeviceId            string    `json:"device_id" form:"device_id"`                           // 设备id
	UserBindChannelId   int64     `json:"user_bind_channel_id" form:"user_bind_channel_id"`     // 用户绑定渠道id
	UserBindChannelName string    `json:"user_bind_channel_name" form:"user_bind_channel_name"` // 用户绑定渠道名称
	Phone               string    `json:"phone" form:"phone"`                                   // 手机号
	RegisterType        int64     `json:"register_type" form:"register_type"`                   // 注册方式，1：一键登录；2：验证码；3：苹果
	RegisterVersion     string    `json:"register_version" form:"register_version"`             // 注册版本
	Channel             string    `json:"channel" form:"channel"`                               // 渠道
	IsDelete            int8      `json:"is_delete" form:"is_delete"`                           // 是否已被删除，0：未删除；1：已注销
	BeginCreateAt       time.Time `json:"begin_create_at" form:"begin_create_at"`               // 创建时间起始点
	EndCreateAt         time.Time `json:"end_create_at" form:"end_create_at"`                   // 创建时间结束点
	IsVip               int8      `json:"is_vip" form:"is_vip"`                                 // 是否是vip，-1：不是；1：是
	VipType             int8      `json:"vip_type" form:"vip_type"`                             // vip类型，1：月卡；2：季卡；3：年卡
}

type UpdateUserRequest struct {
	ID         int64  `json:"id" binding:"required"`
	Username   string `json:"username"`
	Avatar     string `json:"avatar"`
	Phone      string `json:"phone"`
	IsDelete   int8   `json:"is_delete"`
	IpLocation string `json:"ip_location"`
	IsVip      int8   `json:"is_vip"`
	VipType    int8   `json:"vip_type"` // vip类型，1：月卡；2：季卡；3：年卡
}

// UserStatisticsDto 用户统计数据DTO
type UserStatisticsDto struct {
	TotalUsers                 int64                     `json:"total_users"`                     // 用户总量
	TotalUsersByChannel        []ChannelUserCountDto     `json:"total_users_by_channel"`          // 各渠道用户量
	MonthlyNewUsers            []MonthlyUserStatsDto     `json:"monthly_new_users"`               // 每月新增用户
	UsersByRegisterVersion     []RegisterVersionCountDto `json:"users_by_register_version"`       // 各注册版本用户量
	YesterdayNewUsers          int64                     `json:"yesterday_new_users"`             // 昨日新增用户量
	YesterdayNewUsersByChannel []ChannelUserCountDto     `json:"yesterday_new_users_by_channel"`  // 昨日各渠道新增用户
	ThisMonthNewUsers          int64                     `json:"this_month_new_users"`            // 本月新增用户量
	ThisMonthNewUsersByChannel []ChannelUserCountDto     `json:"this_month_new_users_by_channel"` // 本月各渠道新增用户
	TodayNewUsers              int64                     `json:"today_new_users"`                 // 本日新增用户量
	TodayNewUsersByChannel     []ChannelUserCountDto     `json:"today_new_users_by_channel"`      // 本日各渠道新增用户
	ThisMonthDailyNewUsers     []DailyUserStatsDto       `json:"this_month_daily_new_users"`      // 本月每日新增用户统计
}

// ChannelUserCountDto 渠道用户数量DTO
type ChannelUserCountDto struct {
	ChannelID   int64  `json:"channel_id"`   // 渠道ID
	ChannelName string `json:"channel_name"` // 渠道名称
	UserCount   int64  `json:"user_count"`   // 用户数量
}

// MonthlyUserStatsDto 月度用户统计DTO
type MonthlyUserStatsDto struct {
	Year              int                   `json:"year"`                 // 年份
	Month             int                   `json:"month"`                // 月份
	TotalNewUsers     int64                 `json:"total_new_users"`      // 总新增用户
	NewUsersByChannel []ChannelUserCountDto `json:"new_users_by_channel"` // 各渠道新增用户
}

// RegisterVersionCountDto 注册版本用户数量DTO
type RegisterVersionCountDto struct {
	RegisterVersion string `json:"register_version"` // 注册版本
	UserCount       int64  `json:"user_count"`       // 用户数量
}

// DailyUserStatsDto 每日用户统计DTO
type DailyUserStatsDto struct {
	Year              int                   `json:"year"`                 // 年份
	Month             int                   `json:"month"`                // 月份
	Day               int                   `json:"day"`                  // 日期
	TotalNewUsers     int64                 `json:"total_new_users"`      // 总新增用户
	NewUsersByChannel []ChannelUserCountDto `json:"new_users_by_channel"` // 各渠道新增用户
}
