package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"gorm.io/gorm"
)

type userBindGetuiDao struct {
	db  *gorm.DB
	log *logger.Logger
}

func NewUserBindGetuiRepo(bootStrap *component.BootStrap) repo.UserBindGetuiRepo {
	return &userBindGetuiDao{
		db:  bootStrap.Driver.GetMysqlDb(),
		log: bootStrap.Log,
	}
}

func (d *userBindGetuiDao) GetBindInfoByUserId(userId int64) (*dto.UserBindGetuiInfoDto, error) {
	var cid model.UserBindGetui
	if err := d.db.Model(&model.UserBindGetui{}).Where("user_id = ?", userId).Find(&cid).Error; err != nil {
		logger.Log().Error("获取用户cid异常: %v", err.Error())
		return nil, err
	}
	return &dto.UserBindGetuiInfoDto{
		Id:     cid.Id,
		UserId: cid.UserId,
		Cid:    cid.ClientId,
	}, nil
}

func (d *userBindGetuiDao) GetBindInfoByCid(cid string) (*dto.UserBindGetuiInfoDto, error) {
	var user model.UserBindGetui
	if err := d.db.Model(&model.UserBindGetui{}).Where("client_id = ?", cid).Find(&user).Error; err != nil {
		logger.Log().Error("获取用户cid异常: %v", err.Error())
		return nil, err
	}
	return &dto.UserBindGetuiInfoDto{
		Id:     user.Id,
		UserId: user.UserId,
		Cid:    user.ClientId,
	}, nil
}

func (d *userBindGetuiDao) DeleteBindByUserId(userId int64) error {
	if err := d.db.Where("user_id = ?", userId).Delete(&model.UserBindGetui{}).Error; err != nil {
		logger.Log().Error("删除用户cid异常: %v", err.Error())
		return err
	}
	return nil
}

func (d *userBindGetuiDao) BindUserIdAndCid(userId int64, cid string) error {
	if err := d.db.Create(&model.UserBindGetui{
		UserId:   userId,
		ClientId: cid,
	}).Error; err != nil {
		logger.Log().Error("绑定用户cid异常: %v", err.Error())
		return err
	}
	return nil
}

func (d *userBindGetuiDao) UpdateBind(userId int64, cid string) error {
	if err := d.db.Model(&model.UserBindGetui{}).Where("user_id = ?", userId).Update("client_id", cid).Error; err != nil {
		logger.Log().Error("更新用户cid异常: %v", err.Error())
		return err
	}
	return nil
}

func (d *userBindGetuiDao) Page(req *dto.GetuiListRequest) ([]*dto.UserBindGetuiInfoDto, int64, error) {
	var users []*model.UserBindGetui
	var total int64
	query := d.db.Model(&model.UserBindGetui{})

	// 构建查询条件
	if req.UserId != 0 {
		query = query.Where("user_id = ?", req.UserId)
	}

	if req.Cid != "" {
		query = query.Where("client_id = ?", req.Cid)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		d.log.Error("查询总数失败: %v", err)
		return nil, 0, err
	}

	// 获取列表
	offset := (req.Page - 1) * req.Size
	err = query.Offset(offset).Limit(req.Size).Order("create_at DESC").Find(&users).Error
	if err != nil {
		d.log.Error("查询列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为DTO
	var dtoList []*dto.UserBindGetuiInfoDto
	for _, user := range users {
		dtoList = append(dtoList, &dto.UserBindGetuiInfoDto{
			Id:       user.Id,
			UserId:   user.UserId,
			Cid:      user.ClientId,
			CreateAt: user.CreateAt,
			UpdateAt: user.UpdateAt,
		})
	}

	return dtoList, total, nil
}
