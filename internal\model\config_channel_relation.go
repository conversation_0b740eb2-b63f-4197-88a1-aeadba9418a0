package model

import "time"

type ConfigChannelRelation struct {
	ID                 int64            `json:"id" gorm:"primaryKey;autoIncrement;column:id"`                                // 主键ID
	ConfigID           int              `json:"config_id" gorm:"primaryKey;column:config_id;not null"`                       // 配置ID，关联config表
	MarketingChannelID int              `json:"marketing_channel_id" gorm:"primaryKey;column:marketing_channel_id;not null"` // 营销渠道ID，关联marketing_channel表
	CreateAt           time.Time        `json:"create_at" gorm:"column:create_at;autoCreateTime"`                            // 创建时间
	Config             Config           `gorm:"foreignKey:ConfigID"`                                                         // 外键关联配置表
	MarketingChannel   MarketingChannel `gorm:"foreignKey:MarketingChannelID"`                                               // 外键关联营销渠道表
}

func (m *ConfigChannelRelation) TableName() string {
	return "config_channel_relation"
}
