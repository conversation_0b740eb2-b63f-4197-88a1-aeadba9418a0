package dto

import (
	"chongli/internal/model"
	"time"
)

// AdminUserDto 后台管理用户DTO
type AdminUserDto struct {
	ID            int64            `json:"id"`              // 主键id
	Email         string           `json:"email"`           // 邮箱地址
	Nickname      string           `json:"nickname"`        // 用户昵称
	Avatar        string           `json:"avatar"`          // 头像地址
	LastLoginTime *time.Time       `json:"last_login_time"` // 最后登录时间
	LastLoginIP   string           `json:"last_login_ip"`   // 最后登录IP
	LoginCount    int64            `json:"login_count"`     // 登录次数
	CreatedBy     int64            `json:"created_by"`      // 创建者ID
	UpdatedBy     int64            `json:"updated_by"`      // 更新者ID
	CreateAt      time.Time        `json:"create_at"`       // 创建时间
	UpdateAt      time.Time        `json:"update_at"`       // 更新时间
	IsDelete      model.StatusFlag `json:"is_delete"`       // 是否已被删除

	// 向后兼容：始终返回启用状态，不存储在数据库中
	// @deprecated 此字段已废弃，始终返回1（启用状态），将在后续版本中移除
	Status model.StatusFlag `json:"status"` // 用户状态：固定返回1（启用）
}

// AdminLoginRequestDto 后台用户登录请求DTO
type AdminLoginRequestDto struct {
	Email string `json:"email" binding:"required,email"` // 邮箱地址
	Code  string `json:"code" binding:"required,len=6"`  // 验证码
}

// AdminLoginResponseDto 后台用户登录响应DTO
type AdminLoginResponseDto struct {
	Token    string        `json:"token"`     // JWT Token
	UserInfo *AdminUserDto `json:"user_info"` // 用户信息
}

// AdminSendCodeRequestDto 发送验证码请求DTO
type AdminSendCodeRequestDto struct {
	Email   string `json:"email" binding:"required,email"`         // 邮箱地址
	Purpose int8   `json:"purpose" binding:"required,min=1,max=3"` // 用途：1-登录，2-重置密码，3-其他
}

// AdminSendCodeResponseDto 发送验证码响应DTO
type AdminSendCodeResponseDto struct {
	Message   string    `json:"message"`    // 响应消息
	ExpiresAt time.Time `json:"expires_at"` // 验证码过期时间
}

// EmailVerifyCodeDto 邮箱验证码DTO
type EmailVerifyCodeDto struct {
	ID        int64            `json:"id"`         // 主键id
	Email     string           `json:"email"`      // 邮箱地址
	Code      string           `json:"code"`       // 验证码
	Purpose   int8             `json:"purpose"`    // 用途
	ExpiredAt time.Time        `json:"expired_at"` // 过期时间
	IsUsed    model.StatusFlag `json:"is_used"`    // 是否已使用
	ClientIP  string           `json:"client_ip"`  // 客户端IP
	CreateAt  time.Time        `json:"create_at"`  // 创建时间
	UpdateAt  time.Time        `json:"update_at"`  // 更新时间
}

// AdminLoginLogDto 后台登录日志DTO
type AdminLoginLogDto struct {
	ID        int64            `json:"id"`         // 主键id
	UserID    int64            `json:"user_id"`    // 用户ID
	Email     string           `json:"email"`      // 登录邮箱
	LoginIP   string           `json:"login_ip"`   // 登录IP
	UserAgent string           `json:"user_agent"` // 用户代理
	Status    model.StatusFlag `json:"status"`     // 登录状态：-1-失败，1-成功
	Message   string           `json:"message"`    // 登录消息
	CreateAt  time.Time        `json:"create_at"`  // 创建时间
}

// AdminUserListRequestDto 后台用户列表请求DTO
type AdminUserListRequestDto struct {
	Page     int    `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize int    `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页大小
	Email    string `json:"email" form:"email"`                                 // 邮箱筛选
}

// AdminUserListResponseDto 后台用户列表响应DTO
type AdminUserListResponseDto struct {
	List     []*AdminUserDto `json:"list"`      // 用户列表
	Total    int64           `json:"total"`     // 总数
	Page     int             `json:"page"`      // 当前页码
	PageSize int             `json:"page_size"` // 每页大小
}

// AdminUserCreateRequestDto 创建后台用户请求DTO
type AdminUserCreateRequestDto struct {
	ID       *int64 `json:"id,omitempty"`                             // 用户ID（可选，提供时为修改，不提供时为新增）
	Email    string `json:"email" binding:"required,email"`           // 邮箱地址
	Nickname string `json:"nickname" binding:"required,min=2,max=50"` // 用户昵称
	Avatar   string `json:"avatar"`                                   // 头像地址
	IsDelete int8   `json:"is_delete" binding:"oneof=-1 1"`           // 是否删除（只能是1或者-1）
}

// AdminUserUpdateRequestDto 更新后台用户请求DTO
type AdminUserUpdateRequestDto struct {
	ID       int64  `json:"id" binding:"required,min=1"`              // 用户ID
	Nickname string `json:"nickname" binding:"required,min=2,max=50"` // 用户昵称
	Avatar   string `json:"avatar"`                                   // 头像地址
}
