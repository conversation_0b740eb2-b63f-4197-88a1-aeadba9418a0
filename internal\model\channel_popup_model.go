package model

import "time"

// ChannelPopup 结构体对应 channel_popup 表
type ChannelPopup struct {
	ID        int       `gorm:"primaryKey;column:id" json:"id"`
	ChannelID int       `gorm:"column:channel_id" json:"channel_id"`
	PopupID   int       `gorm:"column:popup_id" json:"popup_id"`
	CreateAt  time.Time `gorm:"column:create_at;autoCreateTime" json:"create_at"`
	UpdateAt  time.Time `gorm:"column:update_at;autoUpdateTime" json:"update_at"`
}

// TableName 设置 ChannelPopup 结构体对应的表名
func (ChannelPopup) TableName() string {
	return "channel_popup"
}
