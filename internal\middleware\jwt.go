package middleware

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/internal/model"
	"chongli/internal/service"
	errpkg "chongli/pkg/error"
	"chongli/pkg/jwt"
	"chongli/pkg/logger"
	resp "chongli/pkg/response"
	"github.com/gin-gonic/gin"
)

type JWTAuthMiddleware struct {
	log         *logger.Logger
	userService *service.UserService
}

func NewJWTAuthMiddleware(
	bootStrap *component.BootStrap,
	userService *service.UserService,
) *JWTAuthMiddleware {
	return &JWTAuthMiddleware{
		log:         bootStrap.Log,
		userService: userService,
	}
}

// JWTAuth jwt authentication
func (jm *JWTAuthMiddleware) JWTAuth() gin.HandlerFunc {
	claimKey := "claim"
	return func(c *gin.Context) {
		token := c.Request.Header.Get("X-Token")
		if token == "" {
			err := errpkg.NewMiddleError(resp.PermissionDeniedError)
			resp.Response(c, nil, nil, err, resp.WithSLSLog)
			c.Abort()
			return
		}

		jwtSecret := apollo.GetApolloConfig().JwtSecret
		j := jwt.NewJWT(jwtSecret)
		claims, errParse := j.ParserToken(token)
		if errParse != nil {
			// token过期
			if errParse.Error() == jwt.ValidationErrorExpired {
				err := errpkg.NewMiddleError(resp.TokenExpiredError)
				resp.Response(c, nil, nil, err, resp.WithSLSLog)
				c.Abort()
				return
			}
			err := errpkg.NewMiddleErrorWithCause(errParse, resp.TokenError)
			resp.Response(c, nil, nil, err, resp.WithSLSLog)
			c.Abort()
			return
		}

		// 用户注销校验
		userId := claims.MetaData["user_id"].(float64)
		userInfo, err := jm.userService.GetUserInfoByUid(int64(userId))
		if err != nil {
			err := errpkg.NewMiddleErrorWithCause(err, resp.TokenError)
			resp.Response(c, nil, nil, err, resp.WithSLSLog)
			c.Abort()
			return
		}
		if userInfo.IsDelete == int8(model.StatusEnabled) {
			resp.Response(c, nil, nil, errpkg.NewMiddleError(resp.TokenExpiredError), resp.WithSLSLog)
			c.Abort()
			return
		}

		// set claims to context.
		jm.setData2Context(c, claimKey, claims)
	}
}

// setData2Context set data to context.
func (jm *JWTAuthMiddleware) setData2Context(c *gin.Context, claimKey string, claims *jwt.CustomClaims) {
	// 将解析后的有效载荷claims重新写入gin.Context引用对象中
	// 将user_id 和 uuid 写进ctx
	meta := claims.MetaData
	if uid, ok := meta["user_id"]; !ok {
		err := errpkg.NewMiddleError(resp.TokenError)
		resp.Response(c, nil, nil, err, resp.WithSLSLog)
		c.Abort()
		return
	} else {
		c.Set("user_id", uid)
	}
	c.Set(claimKey, claims)
}
