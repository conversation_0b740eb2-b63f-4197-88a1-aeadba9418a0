package utils

import (
	"chongli/pkg/aliyun"
	"crypto/rand"
	"crypto/sha256"
	"errors"
	"fmt"
	"math/big"
	"path"
	"reflect"
	"regexp"
	"runtime/debug"
	"strconv"
	"strings"
	"unicode"
	"unicode/utf8"

	"chongli/component/apollo"

	"github.com/alibabacloud-go/green-20220302/v2/client"
	"github.com/gin-gonic/gin"
	"github.com/hashicorp/go-version"

	"chongli/pkg/logger"

	uuid "github.com/satori/go.uuid"
)

// CreateUUid uuid
func CreateUUid() string {
	return strings.ReplaceAll(uuid.NewV4().String(), "-", "")
}

func IsStruct(i interface{}) bool {
	eType := reflect.TypeOf(i)
	for eType.Kind() == reflect.Ptr {
		eType = eType.Elem()
	}
	return eType.Kind() == reflect.Struct
}

func HasField(obj interface{}, field string) bool {
	var ok bool
	eType := reflect.TypeOf(obj)
	for eType.Kind() == reflect.Ptr {
		eType = eType.Elem()
	}
	_, ok = eType.FieldByName(field)
	return ok
}

func SetFieldValue(obj interface{}, fieldName string, value interface{}) error {
	objValue := reflect.ValueOf(obj)
	if objValue.Kind() == reflect.Ptr {
		objValue = objValue.Elem()
	}

	fieldValue := objValue.FieldByName(fieldName)
	if !fieldValue.IsValid() {
		return fmt.Errorf("field %s not found", fieldName)
	}

	if !fieldValue.CanSet() {
		return fmt.Errorf("field %s cannot be set", fieldName)
	}

	inValue := reflect.ValueOf(value)
	if fieldValue.Type() != inValue.Type() {
		return fmt.Errorf("provided value type %s doesn't match field type %s", inValue.Type(), fieldValue.Type())
	}

	fieldValue.Set(inValue)
	return nil
}

// Go 安全启用goroutine
func Go(fn func()) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Log().LogCode("goroutine", fmt.Sprintf("调用go协程异常:[%v]", string(debug.Stack())))
			}
		}()
		fn()
	}()
}

// StructFieldNames 获取结构体中包含的字段名(取json tag)（不包含二级）
func StructFieldNames(s interface{}) []string {
	t := reflect.TypeOf(s)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	var fieldNames []string
	for i := 0; i < t.NumField(); i++ {
		fieldNames = append(fieldNames, t.Field(i).Tag.Get("json"))
	}
	return fieldNames
}

// GetRandomString 生成随机字符串
func GetRandomString(l int) string {
	const letters = "0123456789abcdefghijklmnopqrstuvwxyz"
	ret := make([]byte, l)

	for i := 0; i < l; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		if err != nil {
			panic(fmt.Errorf("failed to generate random number for GetRandomString: %w", err))
		}
		ret[i] = letters[num.Int64()]
	}
	return string(ret)
}

// SHA256 SHA256加密
func SHA256(str string) string {
	// 创建SHA256哈希对象
	hash := sha256.New()
	// 写入数据
	hash.Write([]byte(str))
	// 获取哈希值
	value := hash.Sum(nil)
	return string(value)
}

// GenValidateCode 生成随机数字验证码
func GenValidateCode(width int) string {
	numeric := [10]byte{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}
	r := len(numeric)
	var sb strings.Builder
	for i := 0; i < width; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(r)))
		if err != nil {
			panic(fmt.Errorf("failed to generate random number for GenValidateCode: %w", err))
		}
		_, _ = fmt.Fprintf(&sb, "%d", numeric[num.Int64()])
	}
	return sb.String()
}

// HandleTextDetectionPlusLabels 歌词检测后的处理
func HandleTextDetectionPlusLabels(results []*client.TextModerationPlusResponseBodyDataResult) string {
	aliErrs := map[string]string{
		"pornographic_adult":           "疑似色情内容",
		"sexual_terms":                 "疑似性健康内容",
		"sexual_suggestive":            "疑似低俗内容",
		"political_figure":             "疑似政治人物",
		"political_entity":             "疑似政治实体",
		"political_n":                  "疑似敏感政治内容",
		"political_p":                  "疑似涉政禁宣人物",
		"political_a":                  "涉政专项升级保障",
		"violent_extremist":            "疑似极端组织",
		"violent_incidents":            "疑似极端主义内容",
		"violent_weapons":              "疑似武器弹药",
		"contraband_drug":              "疑似毒品相关",
		"contraband_gambling":          "疑似赌博相关",
		"contraband_act":               "疑似违禁行为",
		"contraband_entity":            "疑似违禁工具",
		"inappropriate_discrimination": "疑似偏见歧视内容",
		"inappropriate_ethics":         "疑似不良价值观内容",
		"inappropriate_profanity":      "疑似攻击辱骂内容",
		"inappropriate_oral":           "疑似低俗口头语内容",
		"inappropriate_superstition":   "疑似封建迷信内容",
		"inappropriate_nonsense":       "疑似无意义灌水内容",
		"pt_to_sites":                  "疑似站外引流",
		"pt_by_recruitment":            "疑似网赚兼职广告",
		"pt_to_contact":                "疑似引流广告号",
		"religion_b":                   "疑似涉及佛教",
		"religion_t":                   "疑似涉及道教",
		"religion_c":                   "疑似涉及基督教",
		"religion_i":                   "疑似涉及伊斯兰教",
		"religion_h":                   "疑似涉及印度教",
		"customized":                   "命中自定义词库",
	}
	responseErr := ""
	for _, result := range results {
		labels := strings.Split(*result.Label, ",")
		if len(labels) > 0 {
			for _, label := range labels {
				if aliErr, ok := aliErrs[label]; ok {
					responseErr += aliErr + ";"
				}
			}
		}
	}
	return responseErr
}

type RequestHeaderDto struct {
	Version   string `json:"version"`
	Channel   string `json:"channel"`
	DeviceId  string `json:"deviceId"`
	Signature string `json:"signature"`
}

// RequestHeader 获取请求头信息
func RequestHeader(c *gin.Context) (data *RequestHeaderDto) {
	return &RequestHeaderDto{
		Version:   strings.TrimSpace(c.Request.Header.Get("version")),
		Channel:   strings.TrimSpace(c.Request.Header.Get("channel")),
		DeviceId:  strings.TrimSpace(c.Request.Header.Get("deviceId")),
		Signature: strings.TrimSpace(c.Request.Header.Get("signature")),
	}
}

// ClearPlatform 清洗 platform 不管ios 安卓大写小写，或者不符合这个规范的，一律转换为小写，字母对不上的返回错误
func ClearPlatform(platform string) (string, error) {
	const (
		Android = "android"
		IOS     = "ios"
	)
	platform = strings.ToLower(platform)
	if strings.Contains(platform, "ios") {
		return IOS, nil
	} else if strings.Contains(platform, "android") {
		return Android, nil
	}
	return "", errors.New("platform must be ios or android")
}

// GetUserId 获取登录用户的 userId
func GetUserId(c *gin.Context) int64 {
	userId, ok := c.Get("user_id")
	if !ok {
		return -1
	}
	return int64(userId.(float64))
}

// IsValidPhoneNumber 校验手机号码格式
func IsValidPhoneNumber(phone string) bool {
	// 定义正则表达式，匹配11位数字且以1开头
	pattern := `^1[3456789]\d{9}$`
	reg := regexp.MustCompile(pattern)
	return reg.MatchString(phone)
}

// SmsSHA256 发送验证码之前的SHA256加密获取签名
func SmsSHA256(phone, timestamp string) string {
	secretKey := apollo.GetApolloConfig().PhoneCodeSecret
	dataToHash := "phone=" + phone +
		"&secret_key=" + secretKey +
		"&timestamp=" + timestamp
	return fmt.Sprintf("%x", sha256.Sum256([]byte(dataToHash)))
}

// CheckTextLength 校验文本长度是否不在 min~max
func CheckTextLength(text string, minLength, maxLength int) bool {
	if utf8.RuneCountInString(text) < minLength || utf8.RuneCountInString(text) >= maxLength {
		return true
	}
	return false
}

// TextDetection 文本检测
// func TextDetection(text string) (result string, err error) {
// 	resp, _err := aliyun.TextDetectionPlus(text)
// 	if _err != nil {
// 		return "", _err
// 	}
// 	if resp == nil {
// 		return "", nil
// 	}
// 	// 歌词违规提醒
// 	if result = HandleTextDetectionPlusLabels(resp.Body.Data.Result); result != "" {
// 		return result, nil
// 	}
// 	return "", nil
// }

func IsAllEnglish(text string) bool {
	for _, r := range text {
		if unicode.Is(unicode.Han, r) {
			// 如果包含中文字符，返回false（不是全英文）
			return false
		}
	}
	// 没有中文字符，返回true（是全英文）
	return true
}

func GetFileExtension(filename string) string {
	return path.Ext(filename)
}

func IsHTTPsUrl(url string) bool {
	return strings.HasPrefix(strings.ToLower(url), "http://") || strings.HasPrefix(strings.ToLower(url), "https://")
}

func EnsureHttpsPrefix(url string) string {
	if len(url) == 0 {
		return ""
	}
	if !IsHTTPsUrl(url) {
		return "https://" + url
	}
	return url
}

// StringToInt64 将字符串转换为int64类型
func StringToInt64(s string) (int64, error) {
	if s == "" {
		return 0, errors.New("empty string")
	}
	result, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return 0, err
	}
	return result, nil
}

// CheckVersion ddd.ddd.ddd 小数点前后都是三位数字，且不能以0开头 如 1.2.01 但可以是1.2.0
func CheckVersion(version string) error {
	var versionRegex = regexp.MustCompile(`^([1-9][0-9]{0,2})\.([1-9][0-9]{0,2}|0)\.([1-9][0-9]{0,2}|0)$`)
	if !versionRegex.MatchString(version) {
		return errors.New("非法版本号")
	}
	return nil
}

// VersionToVersionInt 将版本字符串转换为整数，使用 hashicorp/go-version 库
// 例如: "1.2.3" -> 1002003, "2.10.15" -> 2010015
// 该函数支持语义化版本控制，能正确处理各种版本格式
func VersionToVersionInt(versionStr string) int64 {
	if versionStr == "" {
		return 0
	}

	// 使用 hashicorp/go-version 解析版本
	v, err := version.NewVersion(versionStr)
	if err != nil {
		// 如果解析失败，返回 0
		return 0
	}

	// 获取版本的各个部分
	segments := v.Segments()
	if len(segments) == 0 {
		return 0
	}

	// 确保至少有3个部分 (major.minor.patch)
	for len(segments) < 3 {
		segments = append(segments, 0)
	}

	var result int64
	for i := 0; i < 3 && i < len(segments); i++ {
		num := int64(segments[i])

		// 限制每个部分最大为999
		if num > 999 {
			num = 999
		}

		// 计算权重: major * 1000000 + minor * 1000 + patch
		switch i {
		case 0: // major
			result += num * 1000000
		case 1: // minor
			result += num * 1000
		case 2: // patch
			result += num
		}
	}

	return result
}

var ContentRiskDescriptions = map[string]string{
	// 色情相关风险
	"pornographic_adultContent":     "疑似含有成人色情内容。",
	"pornographic_adultContent_tii": "图中文字疑似含有色情内容。",
	"sexual_suggestiveContent":      "疑似含有疑似低俗或性暗示内容。",
	"sexual_partialNudity":          "疑似含有包含肢体裸露或性感内容。",

	// 政治相关风险
	"political_politicalFigure":              "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_name_tii":     "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_metaphor_tii": "疑似包含政治敏感，无法生成请修改后再试",
	"political_TVLogo":                       "疑似包含政治敏感，无法生成请修改后再试",
	"political_map":                          "疑似包含政治敏感，无法生成请修改后再试",
	"political_outfit":                       "疑似包含政治敏感，无法生成请修改后再试",
	"political_prohibitedPerson":             "疑似包含政治敏感，无法生成请修改后再试",
	"political_prohibitedPerson_tii":         "疑似包含政治敏感，无法生成请修改后再试",
	"political_taintedCelebrity":             "疑似包含政治敏感，无法生成请修改后再试",
	"political_taintedCelebrity_tii":         "疑似包含政治敏感，无法生成请修改后再试",
	"political_flag":                         "疑似包含政治敏感，无法生成请修改后再试",
	"political_historicalNihility":           "疑似包含政治敏感，无法生成请修改后再试",
	"political_historicalNihility_tii":       "疑似包含政治敏感，无法生成请修改后再试",
	"political_religion_tii":                 "疑似包含政治敏感，无法生成请修改后再试",
	"political_racism_tii":                   "疑似包含政治敏感，无法生成请修改后再试",
	"political_badge":                        "疑似包含政治敏感，无法生成请修改后再试",

	// 暴力相关风险
	"violent_explosion":       "疑似含有烟火类内容元素。",
	"violent_gunKnives":       "疑似含有刀具、枪支等内容。",
	"violent_gunKnives_tii":   "图中文字疑似含枪支刀具的描述。",
	"violent_armedForces":     "疑似含有武装元素。",
	"violent_crowding":        "疑似包含政治敏感，无法生成请修改后再试",
	"violent_horrificContent": "疑似含有惊悚、血腥等内容。",
	"violent_horrific_tii":    "图中文字疑似描述暴力、恐怖的内容。",

	// 违禁品相关风险
	"contraband_drug":       "含有疑似毒品等内容。",
	"contraband_drug_tii":   "图中文字疑似描述违禁毒品。",
	"contraband_gamble":     "含有疑似赌博等内容。",
	"contraband_gamble_tii": "图中文字疑似描述赌博行为。",

	// 欺诈相关风险
	"fraud_videoAbuse":  "图片疑似有隐藏视频风险。",
	"fraud_playerAbuse": "图片疑似有隐藏播放器风险。",

	"pornographic_adultToys":      "画面疑似含有成人器具内容。",
	"pornographic_cartoon":        "画面疑似含有卡通色情内容。",
	"pornographic_art":            "画面疑似含有艺术品色情内容。",
	"pornographic_suggestive_tii": "图中文字含低俗内容。",
	"pornographic_o_tii":          "图中文字含LGBT类内容。",
	"pornographic_organs_tii":     "图中文字含性器官描述内容。",
	"pornographic_adultToys_tii":  "图中文字含成人玩具类内容。",
	"sexual_femaleUnderwear":      "画面疑似含有内衣泳衣内容。",
	"sexual_cleavage":             "画面疑似含有女性乳沟特征。",
	"sexual_maleTopless":          "画面疑似含有男性赤膊内容。",
	"sexual_cartoon":              "画面疑似含有动漫类性感内容。",
	"sexual_shoulder":             "画面疑似含有肩部性感内容。",
	"sexual_femaleLeg":            "画面疑似含有腿部性感内容。",
	"sexual_pregnancy":            "画面疑似含有孕照哺乳内容。",
	"sexual_kiss":                 "画面疑似含有亲吻内容。",
	"sexual_intimacy":             "画面疑似含有亲密行为内容。",
	"sexual_intimacyCartoon":      "画面疑似含有卡通动漫亲密动作。",

	"political_prohibitedPerson_1": "画面疑似含有国家级落马官员。",
	"political_prohibitedPerson_2": "画面疑似含有省市级落马官员。",
	"political_CNFlag":             "画面疑似含有中国国旗。",
	"political_CNMap":              "画面疑似含有中国地图。",
	"political_logo":               "画面疑似含有禁宣媒体标识。",

	"violent_weapon":                "画面疑似包含军器装备。",
	"violent_gun":                   "画面疑似包含枪支。",
	"violent_knives":                "画面疑似包含刀具。",
	"violent_horrific":              "画面疑似包含惊悚内容。",
	"violent_nazi":                  "画面疑似包含特殊内容。",
	"violent_blood":                 "画面疑似包含血腥内容。",
	"violent_extremistGroups_tii":   "图中文字含暴恐组织内容。",
	"violent_extremistIncident_tii": "图中文字含暴恐事件内容。",
	"violence_weapons_tii":          "图中文字疑似含枪支刀具的描述。",
	"violent_ACU":                   "画面疑似包含作战服。",

	"flag_country":               "疑似包含政治敏感，无法生成请修改后再试",
	"inappropriate_smoking":      "画面疑似含有烟相关内容。",
	"inappropriate_drinking":     "画面疑似含有酒相关内容。",
	"inappropriate_tattoo":       "画面疑似含有纹身内容。",
	"inappropriate_middleFinger": "画面疑似含有竖中指内容。",
	"inappropriate_foodWasting":  "画面疑似含有浪费粮食内容。",
	"profanity_offensive_tii":    "图中文字疑似含有较严重辱骂，言语攻击等内容。",
	"profanity_oral_tii":         "图中文字疑似含有口头语性质的辱骂。",

	"political_politicalFigure_1": "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_2": "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_3": "疑似包含政治敏感，无法生成请修改后再试",
	"political_politicalFigure_4": "疑似包含政治敏感，无法生成请修改后再试",
	"religion_clothing":           "图中文字疑似含特定元素或者信息",
	"religion_logo":               "图中文字疑似含特定元素或者信息",
	"religion_flag":               "图中文字疑似含特定元素或者信息",
	"religion_taboo1_tii":         "图中文字疑似含特定元素或者信息",
	"religion_taboo2_tii":         "图中文字疑似含特定元素或者信息",
}

func CheckImage(url string) (result string, err error) {
	url = EnsureHttpsPrefix(url)
	resp, _err := aliyun.ImageDetection(url)
	if _err != nil {
		logger.Log().Error("CheckImage error: %v", _err)
		return "", _err
	}
	if resp == nil {
		// 记录一个警告日志，方便排查问题
		logger.Log().Info("CheckImage warning: nil response without error for url: %s", url)
		return "", nil
	}
	if resp.Body.Code != nil && *resp.Body.Code != 200 {
		logger.Log().Error("CheckImage error: response body is nil")
		return "", fmt.Errorf("图片检测响应体为空")
	}
	// 检查 resp.Body 和 resp.Body.Data 是否为 nil
	if resp.Body == nil || resp.Body.Data == nil {
		logger.Log().Error("CheckImage error: response body or data is nil")
		return "", fmt.Errorf("图片检测响应体为空")
	}
	num := float32(0)
	label := ""

	for _, v := range resp.Body.Data.Result {
		if v.Confidence == nil {
			continue
		}
		if *v.Confidence > num {
			num = *v.Confidence
			label = *v.Label
		}
	}
	if len(label) == 0 {
		return "", nil
	}
	result, exists := ContentRiskDescriptions[label]
	if !exists && label != "" {
		result = "未知风险内容"
	}
	return result, nil
}
