package biz

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/dao"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"chongli/pkg/utils"
	"context"
	"fmt"
	"time"
)

type UserWorkBiz struct {
	userWorkRepo    repo.UserWorkRepo
	userService     *service.UserService
	taskService     *service.TaskService
	taskStepRepo    repo.TaskStepRepo
	tx              driver.ITransaction
	templateService *service.TemplateService
	templateLogRepo repo.TemplateViewMakeLogRepo
	log             *logger.Logger
}

// WorksRequest 公共接口，用于抽象不同类型的作品创建请求
type WorksRequest interface {
	GetTemplateID() uint64
	GetUserID() int64
	GetPetPic() string
	GetCover() string
	GetRequestHeaderDto() *utils.RequestHeaderDto
}

// WorksConfig 作品创建配置
type WorksConfig struct {
	WorkType                string // "dance" or "pic" or "sing"
	TaskType                string // "dance_work" or "pic_work" or "sing_work"
	DiamondMark             string // 钻石扣减描述
	ExpectedMainClass       int    // 期望的模板主分类：pic=1, dance=2, song=3
	ExpectedDurationMinutes int    // 预期完成时间（分钟）
	CreateTaskSteps         func(req WorksRequest, workID uint64, templateParam *dto.AIPicTemplate) ([]*dto.TaskStepDTO, error)
}

type CreatePicWorksRequest struct {
	TemplateID uint64 `json:"template_id" binding:"required"`
	PersonPic  string `json:"person_pic" binding:"required"`
	PetPic     string `json:"pet_pic" binding:"required"`
	UserID     int64  `json:"user_id"`
	Cover      string
	*utils.RequestHeaderDto
}

// 实现 WorksRequest 接口

func (r *CreatePicWorksRequest) GetTemplateID() uint64 {
	return r.TemplateID
}

func (r *CreatePicWorksRequest) GetUserID() int64 {
	return r.UserID
}

func (r *CreatePicWorksRequest) GetPetPic() string {
	return r.PetPic
}

func (r *CreatePicWorksRequest) GetCover() string {
	return r.Cover
}

func (r *CreatePicWorksRequest) GetRequestHeaderDto() *utils.RequestHeaderDto {
	return r.RequestHeaderDto
}

type CreateDanceWorksRequest struct {
	TemplateID uint64 `json:"template_id" binding:"required"`
	UserID     int64  `json:"user_id"`
	PetPic     string `json:"pet_pic" binding:"required"`
	Cover      string
	*utils.RequestHeaderDto
}

// 实现 WorksRequest 接口

func (r *CreateDanceWorksRequest) GetTemplateID() uint64 {
	return r.TemplateID
}

func (r *CreateDanceWorksRequest) GetUserID() int64 {
	return r.UserID
}

func (r *CreateDanceWorksRequest) GetPetPic() string {
	return r.PetPic
}

func (r *CreateDanceWorksRequest) GetRequestHeaderDto() *utils.RequestHeaderDto {
	return r.RequestHeaderDto
}

func (r *CreateDanceWorksRequest) GetCover() string {
	return r.Cover
}

// 公共的作品创建方法
func (b *UserWorkBiz) createWorksCommon(ctx context.Context, req WorksRequest, config WorksConfig) (err error) {
	tx := b.tx.MysqlDbTxBegin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = r.(error)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()

	templateInfo, templateParam, err := b.templateService.GetAiTemplateById(ctx, req.GetTemplateID(), tx)
	if err != nil {
		return err
	}

	// 验证模板类型是否匹配
	if templateInfo.Category.MainClass != config.ExpectedMainClass {
		b.log.Error("模板类型不匹配: 期望 MainClass=%d, 实际 MainClass=%d, WorkType=%s",
			config.ExpectedMainClass, templateInfo.Category.MainClass, config.WorkType)
		return fmt.Errorf("模板类型与作品类型不匹配")
	}

	// 1. 扣减钻石
	headerDto := req.GetRequestHeaderDto()
	err = b.userService.AddOrSubUserDiamondAndAddRecord(service.DiamondOperationRequest{
		UserID:     req.GetUserID(),
		TemplateID: int64(req.GetTemplateID()),
		Diamond:    uint64(templateInfo.DiamondCost),
		Mark:       config.DiamondMark,
		Version:    headerDto.Version,
		Channel:    headerDto.Channel,
		Op:         int(model.StatusDisabled),
	}, tx)
	if err != nil {
		return err
	}

	// 2. 查询所有用户该分类下未完成的作品数量 (status=0)
	unfinishedQuery := &dto.UserWorks{
		WorkType:  config.WorkType,
		Status:    model.StatusDisabled, // status=0 表示制作中
		IsDeleted: model.StatusDisabled, // 未删除
	}
	unfinishedWorksCount, err := b.userWorkRepo.Count(ctx, unfinishedQuery)
	if err != nil {
		b.log.Error("查询未完成作品数量失败: %v", err)
		unfinishedWorksCount = 1
	}

	// 计算预期完成时间：当前时间 + (未完成作品数 * 单个作品预期时间)
	totalMinutes := unfinishedWorksCount * int64(config.ExpectedDurationMinutes)
	expectedFinishTime := time.Now().Add(time.Duration(totalMinutes) * time.Minute)

	// 写入作品数据
	userWork := &dto.UserWorks{
		UserID:             uint64(req.GetUserID()),
		TemplateID:         req.GetTemplateID(),
		WorkType:           config.WorkType,
		Status:             0,
		Cover:              req.GetPetPic(),
		IsDeleted:          model.StatusDisabled,
		ExpectedFinishTime: &expectedFinishTime,
		Diamond:            int64(templateInfo.DiamondCost),
	}
	err = b.userWorkRepo.Create(ctx, userWork, tx)
	if err != nil {
		b.log.Error("写入作品数据失败: %v", err)
		return err
	}

	// 使用配置中的函数创建任务步骤
	steps, err := config.CreateTaskSteps(req, userWork.ID, templateParam)
	if err != nil {
		b.log.Error("创建任务步骤失败: %v", err)
		return fmt.Errorf("模板参数缺失，子任务创建失败")
	}

	err = b.taskService.CreateSteps(ctx, steps, tx)
	if err != nil {
		return err
	}

	// 3. 记录模板制作量（在成功创建步骤后记录）
	err = b.templateLogRepo.Create(ctx, &dto.TemplateViewMakeLogDTO{
		TemplateID: int64(req.GetTemplateID()),
		Type:       model.MakeTemplateType,
		CreateAt:   time.Now().Unix(),
	}, tx)
	if err != nil {
		b.log.Error("记录模板制作量失败: %v", err)
		return err
	}

	return nil
}

// 创建跳舞任务步骤
func createDanceTaskSteps(req WorksRequest, workID uint64, templateParam *dto.AIPicTemplate) ([]*dto.TaskStepDTO, error) {
	// 检查必要参数
	if templateParam.VideoDesc == "" {
		return nil, fmt.Errorf("模板错误：video_desc不能为空")
	}
	if templateParam.AudioUrl == "" {
		return nil, fmt.Errorf("模板错误：audio_url不能为空")
	}

	taskStepOne := &dto.TaskStepDTO{
		StepIndex: 0,
		StepName:  model.StepNameDanceWork,
		Status:    model.StepStatusPending,
		WorkID:    workID,
		Params: dto.JSONMap{
			"pet_pic":    req.GetPetPic(),
			"video_desc": templateParam.VideoDesc,
			"audio_url":  templateParam.AudioUrl,
		},
		RetryCount: 0,
	}
	return []*dto.TaskStepDTO{taskStepOne}, nil
}

// 创建写真任务步骤
func createPicTaskSteps(req WorksRequest, workID uint64, templateParam *dto.AIPicTemplate) ([]*dto.TaskStepDTO, error) {
	// 需要将 req 转换为 CreatePicWorksRequest 以获取 PersonPic
	picReq := req.(*CreatePicWorksRequest)

	taskStepOne := &dto.TaskStepDTO{
		StepIndex: 0,
		StepName:  model.StepNameAiCompound,
		Status:    model.StepStatusPending,
		WorkID:    workID,
		Params: dto.JSONMap{
			"pet_pic":          picReq.PetPic,
			"pet_desc":         templateParam.PetDesc,
			"background_desc":  templateParam.BackgroundDesc,
			"composition_desc": templateParam.CompositionDesc,
			"style":            templateParam.Style,
			"strength":         templateParam.ModelStrength,
			"workflow_url":     templateParam.WorkflowUrl,
		},
		RetryCount: 0,
	}

	taskStepTwo := dto.TaskStepDTO{
		StepIndex: 1,
		StepName:  model.StepNameChangeStyle,
		Status:    model.StepStatusInit,
		WorkID:    workID,
		Params: dto.JSONMap{
			"style_desc": templateParam.StyleDesc,
		},
		RetryCount: 0,
	}

	stepThree := dto.TaskStepDTO{
		StepIndex: 2,
		StepName:  model.StepNameChangeFace,
		Status:    model.StepStatusInit,
		WorkID:    workID,
		Params: dto.JSONMap{
			"person_pic": picReq.PersonPic,
		},
		RetryCount: 0,
	}

	return []*dto.TaskStepDTO{taskStepOne, &taskStepTwo, &stepThree}, nil
}

func createSingTaskSteps(req WorksRequest, workID uint64, templateParam *dto.AIPicTemplate) ([]*dto.TaskStepDTO, error) {

	if templateParam.AudioUrl == "" {
		return nil, fmt.Errorf("模板错误：audio_url不能为空")
	}

	taskStepOne := &dto.TaskStepDTO{
		StepIndex: 0,
		StepName:  model.StepNameAvatarPicture,
		Status:    model.StepStatusPending,
		WorkID:    workID,
		Params: dto.JSONMap{
			"pet_pic":   req.GetPetPic(),
			"audio_url": templateParam.AudioUrl,
		},
		RetryCount: 0,
	}
	//taskStepTwo := &dto.TaskStepDTO{
	//	StepIndex: 1,
	//	StepName:  model.StepNameAvatarPicture,
	//	Status:    model.StepStatusInit,
	//	WorkID:    workID,
	//	Params: dto.JSONMap{
	//		"audio_url": templateParam.AudioUrl,
	//	},
	//	RetryCount: 0,
	//}
	return []*dto.TaskStepDTO{taskStepOne}, nil
}

func NewUserWorkBiz(
	userWorkRepo repo.UserWorkRepo,
	taskService *service.TaskService,
	taskStepRepo repo.TaskStepRepo,
	bootStrap *component.BootStrap,
	userService *service.UserService,
	templateService *service.TemplateService) *UserWorkBiz {
	return &UserWorkBiz{
		userWorkRepo:    userWorkRepo,
		userService:     userService,
		taskService:     taskService,
		taskStepRepo:    taskStepRepo,
		tx:              bootStrap.Tx,
		templateService: templateService,
		templateLogRepo: dao.NewTemplateViewMakeLogRepo(bootStrap),
		log:             bootStrap.Log,
	}
}

func (b *UserWorkBiz) CreateDanceWorks(ctx context.Context, req *CreateDanceWorksRequest) (err error) {
	config := WorksConfig{
		WorkType:                "dance",
		TaskType:                model.TaskTypeDanceWork,
		DiamondMark:             "制作宠物跳舞扣减钻石",
		ExpectedMainClass:       model.WorkTypeDance,
		ExpectedDurationMinutes: 2, // 跳舞作品预期2分钟
		CreateTaskSteps:         createDanceTaskSteps,
	}
	return b.createWorksCommon(ctx, req, config)
}

func (b *UserWorkBiz) CreatePicWorks(ctx context.Context, req *CreatePicWorksRequest) (err error) {
	config := WorksConfig{
		WorkType:                "pic",
		TaskType:                model.TaskTypePicWork,
		DiamondMark:             "制作写真扣减钻石",
		ExpectedMainClass:       model.WorkTypePic, // pic 对应 MainClass = 1
		ExpectedDurationMinutes: 5,                 // 写真作品预期5分钟
		CreateTaskSteps:         createPicTaskSteps,
	}
	return b.createWorksCommon(ctx, req, config)
}

func (b *UserWorkBiz) CreateSongWorks(ctx context.Context, req *CreateDanceWorksRequest) (err error) {
	config := WorksConfig{
		WorkType:                "sing",
		TaskType:                model.TaskTypeSingWork,
		DiamondMark:             "制作宠物唱歌扣减钻石",
		ExpectedMainClass:       model.WorkTypeSong, // song 对应 MainClass = 2
		ExpectedDurationMinutes: 2,                  // 唱歌作品预期2分钟
		CreateTaskSteps:         createSingTaskSteps,
	}
	return b.createWorksCommon(ctx, req, config)
}

// GetUserWorksList 获取用户作品列表
func (b *UserWorkBiz) GetUserWorksList(ctx context.Context, req *dto.UserWorks) ([]*dto.UserWorks, error) {
	// 执行查询
	list, err := b.userWorkRepo.List(ctx, req, true)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// DeleteUserWork 软删除用户作品
func (b *UserWorkBiz) DeleteUserWork(ctx context.Context, userID int64, workID uint64) error {
	// 先查询作品是否存在且属于当前用户
	work, err := b.userWorkRepo.GetOne(ctx, &dto.UserWorks{ID: workID})
	if err != nil {
		return fmt.Errorf("作品不存在")
	}

	if work.UserID != uint64(userID) {
		return fmt.Errorf("无权限删除此作品")
	}

	if work.IsDeleted == model.StatusEnabled {
		return fmt.Errorf("作品已被删除")
	}

	// 执行软删除
	now := time.Now()
	updates := map[string]any{
		"is_deleted": 1,
		"deleted_at": &now,
	}

	return b.userWorkRepo.Update(ctx, workID, updates)
}
