package processors

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/repo"
)

type PayOrderTaskProcessors struct {
	bootstrap    *component.BootStrap
	payOrderRepo repo.PayOrderRepo
}

// NewPayOrderTaskProcessors 创建支付订单任务处理器实例
func NewPayOrderTaskProcessors(
	bootstrap *component.BootStrap,
	payOrderRepo repo.PayOrderRepo,
) *PayOrderTaskProcessors {
	return &PayOrderTaskProcessors{
		bootstrap:    bootstrap,
		payOrderRepo: payOrderRepo,
	}
}

// CleanExpiredOrders 清理过期订单定时任务
func (s *PayOrderTaskProcessors) CleanExpiredOrders() {
	s.bootstrap.Log.Info("开始执行清理过期订单定时任务")

	// 开启数据库事务
	mysqlTx := s.bootstrap.Tx.MysqlDbTxBegin()

	// 查询所有已过期未支付的支付订单
	expiredOrders, err := s.payOrderRepo.GetExpiredUnpaidOrders(mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("查询过期未支付订单失败: %v", err)
		return
	}

	if len(expiredOrders) == 0 {
		mysqlTx.Rollback()
		s.bootstrap.Log.Info("没有找到过期未支付的订单")
		return
	}

	s.bootstrap.Log.Info("找到 %d 个过期未支付的订单", len(expiredOrders))

	// 提取订单ID列表
	orderIDs := make([]string, 0, len(expiredOrders))
	for _, order := range expiredOrders {
		orderIDs = append(orderIDs, order.OrderID)
	}

	// 批量更新订单状态为已过期，并添加备注
	remark := "用户超过支付时间内未支付，订单已过期"
	if err := s.payOrderRepo.BatchUpdateOrderStateAndRemark(orderIDs, model.OrderStateExpired, remark, mysqlTx); err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("批量更新过期订单状态失败: %v", err)
		return
	}

	// 提交事务
	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("提交事务失败: %v", err)
		return
	}

	s.bootstrap.Log.Info("成功清理 %d 个过期订单", len(orderIDs))
}
