package biz

import (
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"encoding/json"
	"errors"

	"github.com/gin-gonic/gin"
)

type GuiyinApiService struct {
	guiyinDeviceBindRepo repo.GuiyinDeviceBindRepo
	configService        *service.ConfigService
	channelRepo          repo.MarketingChannelRepo
	log                  *logger.Logger
}
type DeviceBindInfo struct {
	DeviceID string `json:"deviceId"`
	AllData  string `json:"allData"`
	Channel  string `json:"channel"`
}

func NewGuiyinService(guiyinDeviceBindRepo repo.GuiyinDeviceBindRepo, configService *service.ConfigService, channelRepo repo.MarketingChannelRepo) *GuiyinApiService {
	return &GuiyinApiService{
		guiyinDeviceBindRepo: guiyinDeviceBindRepo,
		configService:        configService,
		channelRepo:          channelRepo,
		log:                  logger.Log(), // 使用全局logger实例
	}
}

func (g *GuiyinApiService) BindAttributeData(c *gin.Context, bindDto *DeviceBindInfo) (int, error) {
	// 检查设备是否已经绑定
	hasBind, err := g.guiyinDeviceBindRepo.GetGuiyinDeviceBindByDeviceID(bindDto.DeviceID)
	if err != nil {
		return model.DefaultChannelId, err
	}
	if hasBind != nil {
		return hasBind.ChannelId, nil
	}

	// 尝试获取渠道ID，失败时使用0作为默认值
	channelId := g.tryGetChannelId(c, bindDto)

	// 无论是否成功获取到渠道ID，都创建设备绑定记录
	createDto := &dto.GuiyinDeviceBindCreateDto{
		DeviceId:  bindDto.DeviceID,
		AllData:   bindDto.AllData,
		ChannelId: channelId, // 如果获取失败则为0
	}

	deviceBind, err := g.guiyinDeviceBindRepo.CreateGuiyinDeviceBind(createDto)
	if err != nil {
		return model.DefaultChannelId, errors.New("创建设备绑定失败: " + err.Error())
	}

	return deviceBind.ChannelId, nil
}

// tryGetChannelId 尝试获取渠道ID，失败时返回0
func (g *GuiyinApiService) tryGetChannelId(c *gin.Context, bindDto *DeviceBindInfo) int {

	// 获取绑定键配置
	bindKey := g.getBindKey(c)
	if bindKey == "" {
		return model.DefaultChannelId
	}

	// 从JSON中提取绑定值
	bindValue := g.extractBindValue(bindDto.AllData, bindKey)
	if bindValue == "" {
		return model.DefaultChannelId
	}

	// 查询匹配的渠道
	return g.findMatchedChannel(bindKey, bindValue)
}

// getBindKey 获取绑定键配置
func (g *GuiyinApiService) getBindKey(c *gin.Context) string {
	bindKey, err := g.configService.GetConfigByKeyFromCache(c, "guiyin_bind_key")
	if err != nil {
		g.log.Error("获取绑定键配置失败，将使用渠道ID=0: %v", err)
		return ""
	}

	if bindKey == "" {
		g.log.Warning("绑定键配置为空，将使用渠道ID=0")
		return ""
	}

	return bindKey
}

// extractBindValue 从JSON数据中提取绑定值
func (g *GuiyinApiService) extractBindValue(allData, bindKey string) string {
	// 解析AllData JSON字符串
	var allDataMap map[string]interface{}
	if err := json.Unmarshal([]byte(allData), &allDataMap); err != nil {
		g.log.Error("解析AllData JSON失败，将使用渠道ID=0: %v", err)
		return ""
	}

	// 提取bindKey对应的值
	bindKeyValue, ok := allDataMap[bindKey]
	if !ok {
		g.log.Warning("AllData中未找到指定的bindKey: %s，将使用渠道ID=0", bindKey)
		return ""
	}

	// 检查值是否为字符串
	bindKeyValueStr, ok := bindKeyValue.(string)
	if !ok {
		g.log.Warning("bindKey对应的值不是字符串类型，将使用渠道ID=0")
		return ""
	}

	return bindKeyValueStr
}

// findMatchedChannel 查询匹配的营销渠道
func (g *GuiyinApiService) findMatchedChannel(bindKey, bindValue string) int {
	queryDto := &dto.MarketingChannelQueryDto{
		BindKey:   bindKey,
		BindValue: bindValue,
	}

	matchedChannel, err := g.channelRepo.GetMarketingChannel(queryDto)
	if err != nil {
		g.log.Error("查询营销渠道失败，将使用渠道ID=0: %v", err)
		return model.DefaultChannelId
	}

	if matchedChannel == nil {
		g.log.Warning("未找到匹配的营销渠道，bindKey: %s, bindValue: %s，将使用渠道ID=0", bindKey, bindValue)
		return model.DefaultChannelId
	}

	g.log.Info("成功匹配到营销渠道，渠道ID: %d", matchedChannel.ID)
	return matchedChannel.ID
}
