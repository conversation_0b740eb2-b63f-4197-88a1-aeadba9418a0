package processors

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"encoding/json"
	"time"
)

type UserTaskProcessors struct {
	bootstrap   *component.BootStrap
	userService *service.UserService
	userRepo    repo.UserRepo
	userVipRepo repo.UserVipRepo
	configRepo  repo.ConfigRepo
}

// NewUserTaskProcessors 创建用户任务处理器实例
func NewUserTaskProcessors(
	bootstrap *component.BootStrap,
	userRepo repo.UserRepo,
	userVipRepo repo.UserVipRepo,
	configRepo repo.ConfigRepo,
	userService *service.UserService,
) *UserTaskProcessors {
	return &UserTaskProcessors{
		bootstrap:   bootstrap,
		userService: userService,
		userRepo:    userRepo,
		userVipRepo: userVipRepo,
		configRepo:  configRepo,
	}
}

// CheckAndUpdateUserVipStatus 检查并更新会员状态
func (s *UserTaskProcessors) CheckAndUpdateUserVipStatus() {
	s.bootstrap.Log.Info("开始检查并更新会员状态")
	mysqlTx := s.bootstrap.Tx.MysqlDbTxBegin()

	// 读取测试模式配置 vip_test_mode
	config, err := s.configRepo.GetConfigByKey("vip_test_mode", mysqlTx)
	if err != nil {
		s.bootstrap.Log.Error("获取VIP测试模式配置失败: %v", err)
		mysqlTx.Rollback()
		return
	}
	var isTestMode bool
	if config != nil && config.ConfigValue != "" {
		var testModeConfig dto.VipTestModeDto
		if err := json.Unmarshal([]byte(config.ConfigValue), &testModeConfig); err != nil {
			s.bootstrap.Log.Error("解析VIP测试模式配置失败: %v", err)
			mysqlTx.Rollback()
			return
		}
		s.bootstrap.Log.Info("VIP测试模式配置: %+v", testModeConfig)
		isTestMode = testModeConfig.IsTestMode
	}

	if isTestMode {
		// 测试模式：将所有VIP用户提前过期，并赠送完其所有未赠送未删除的钻石记录
		s.bootstrap.Log.Info("VIP测试模式开启：将所有VIP提前过期并赠送完剩余钻石")

		// 查询所有VIP用户（无事务方法）
		vipUsers, err := s.userRepo.GetAllVipUsers()
		if err != nil {
			mysqlTx.Rollback()
			s.bootstrap.Log.Error("获取所有VIP用户失败: %v", err)
			return
		}
		if len(vipUsers) == 0 {
			mysqlTx.Rollback()
			s.bootstrap.Log.Info("当前没有VIP用户，无需处理")
			return
		}

		// 预读取默认赠送配置（用于兼容GiveNum为0的记录）
		defaultGive := dto.DefaultVipGiveDiamondDto{}
		cfg, err := s.configRepo.GetConfigByKey("default_vip_give_diamond", mysqlTx)
		if err == nil && cfg != nil && cfg.ConfigValue != "" {
			_ = json.Unmarshal([]byte(cfg.ConfigValue), &defaultGive)
		}

		// 收集所有需要处理的赠送记录
		allPendingGives := make([]*dto.UserVipGiveDto, 0)
		userIds := make([]int64, 0, len(vipUsers))
		for _, u := range vipUsers {
			userIds = append(userIds, u.ID)

			pending, err := s.userVipRepo.GetPendingVipGivesByUserId(u.ID, mysqlTx)
			if err != nil {
				mysqlTx.Rollback()
				s.bootstrap.Log.Error("查询用户%d待赠送记录失败: %v", u.ID, err)
				return
			}

			for _, g := range pending {
				// 计算需要赠送的钻石数
				monthlyDiamond := uint64(g.GiveNum)
				if monthlyDiamond == 0 {
					switch g.VipType {
					case model.VipTypeMonth:
						monthlyDiamond = defaultGive.MonthGive
					case model.VipTypeQuarter:
						monthlyDiamond = defaultGive.QuarterGive
					case model.VipTypeYear:
						monthlyDiamond = defaultGive.YearGive
					}
				}

				if monthlyDiamond > 0 {
					if err := s.userService.AddOrSubUserDiamondAndAddRecord(service.DiamondOperationRequest{
						UserID:  g.UserId,
						OrderID: g.OrderId,
						GoodsID: g.GoodsId,
						Diamond: monthlyDiamond,
						Mark:    "VIP测试模式提前赠送剩余钻石",
						Op:      int(model.StatusEnabled),
					}, mysqlTx); err != nil {
						s.bootstrap.Log.Error("赠送用户%d钻石失败: %v", g.UserId, err)
						mysqlTx.Rollback()
						return
					}
				}
			}

			// 汇总这些记录以批量更新状态
			allPendingGives = append(allPendingGives, pending...)
		}

		// 批量将这些赠送记录设置为已赠送并已删除
		if len(allPendingGives) > 0 {
			if err := s.userVipRepo.BatchUpdateVipGiveStatusAndIsDelete(allPendingGives, mysqlTx); err != nil {
				mysqlTx.Rollback()
				s.bootstrap.Log.Error("批量更新VIP赠送记录状态失败: %v", err)
				return
			}
		}

		// 批量更新用户会员状态为非VIP
		if err := s.userRepo.BatchUpdateUserInfoByIds(userIds, map[string]any{
			"is_vip":   model.NotVip,
			"vip_type": model.NotVip,
		}, mysqlTx); err != nil {
			mysqlTx.Rollback()
			s.bootstrap.Log.Error("批量更新用户会员状态失败: %v", err)
			return
		}

		// 批量更新用户VIP信息为过期并删除
		if err := s.userVipRepo.BatchUpdateUserVipIsExpireAndIsDelete(userIds, mysqlTx); err != nil {
			mysqlTx.Rollback()
			s.bootstrap.Log.Error("批量更新用户会员信息失败: %v", err)
			return
		}

		// 提交事务
		if err := mysqlTx.Commit().Error; err != nil {
			mysqlTx.Rollback()
			s.bootstrap.Log.Error("提交事务失败: %v", err)
			return
		}

		s.bootstrap.Log.Info("测试模式：共处理VIP用户 %d 位，提前赠送完剩余记录 %d 条并完成过期", len(userIds), len(allPendingGives))
		return
	}

	// 正式模式：仅处理今日到期的会员
	// 获取所有今天到期的会员用户
	now := time.Now()
	today := time.Date(
		now.Year(), now.Month(), now.Day(), // 年月日
		0, 0, 0, 0, // 时分秒纳秒设为0
		now.Location(), // 使用当前时区
	)
	userVips, err := s.userVipRepo.ListTodayExpireVIP(today, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("获取所有会员用户失败: %v", err)
		return
	}

	if len(userVips) == 0 {
		mysqlTx.Rollback()
		s.bootstrap.Log.Info("今天没有到期的会员用户")
		return
	}

	// 批量更新用户会员状态
	userIds := make([]int64, 0, len(userVips))
	for _, userVip := range userVips {
		userIds = append(userIds, userVip.UserId)
	}
	if err := s.userRepo.BatchUpdateUserInfoByIds(userIds, map[string]any{
		"is_vip":   model.NotVip,
		"vip_type": model.NotVip,
	}); err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("批量更新用户会员状态失败: %v", err)
		return
	}

	// 批量更新用户会员信息
	if err := s.userVipRepo.BatchUpdateUserVipIsExpireAndIsDelete(userIds, mysqlTx); err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("批量更新用户会员信息失败: %v", err)
		return
	}

	// 提交事务
	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		s.bootstrap.Log.Error("提交事务失败: %v", err)
		return
	}

	s.bootstrap.Log.Info("成功更新 %d 位会员用户状态", len(userIds))
}

// GiveDiamondForVip 赠送VIP用户钻石
func (s *UserTaskProcessors) GiveDiamondForVip() {
	logger.Log().Info("开始向所有需要赠送钻石的VIP赠送钻石")
	mysqlTx := s.bootstrap.Tx.MysqlDbTxBegin()

	// 获取测试模式配置
	config, err := s.configRepo.GetConfigByKey("vip_test_mode", mysqlTx)
	if err != nil {
		s.bootstrap.Log.Error("获取VIP测试模式配置失败: %v", err)
		mysqlTx.Rollback()
		return
	}

	var isTestMode bool
	if config != nil && config.ConfigValue != "" {
		var testModeConfig dto.VipTestModeDto
		if err := json.Unmarshal([]byte(config.ConfigValue), &testModeConfig); err != nil {
			s.bootstrap.Log.Error("解析VIP测试模式配置失败: %v", err)
			mysqlTx.Rollback()
			return
		}
		isTestMode = testModeConfig.IsTestMode
	}

	// 查询需要赠送钻石的VIP
	now := time.Now()
	var giveVip []*dto.UserVipGiveDto

	if isTestMode {
		// 测试模式：获取当前小时内需要赠送钻石的VIP
		giveVip, err = s.userVipRepo.ListCurrentHourGiveVIP(now, mysqlTx)
		if err != nil {
			s.bootstrap.Log.Error("查询当前小时需要赠送钻石的VIP失败: %v", err)
			mysqlTx.Rollback()
			return
		}
		s.bootstrap.Log.Info("当前小时需要赠送钻石的VIP数量: %d", len(giveVip))
	} else {
		// 正式模式：获取今天需要赠送钻石的VIP
		today := time.Date(
			now.Year(), now.Month(), now.Day(), // 年月日
			0, 0, 0, 0, // 时分秒纳秒设为0
			now.Location(), // 使用当前时区
		)
		giveVip, err = s.userVipRepo.ListTodayGiveVIP(today, mysqlTx)
		if err != nil {
			s.bootstrap.Log.Error("查询今天需要赠送钻石的VIP失败: %v", err)
			mysqlTx.Rollback()
			return
		}
		s.bootstrap.Log.Info("今天需要赠送钻石的VIP数量: %d", len(giveVip))
	}

	if len(giveVip) == 0 {
		mysqlTx.Rollback()
		if isTestMode {
			s.bootstrap.Log.Info("当前小时没有需要赠送钻石的VIP")
		} else {
			s.bootstrap.Log.Info("今天没有需要赠送钻石的VIP")
		}
		return
	}

	// 获取配置的默认VIP赠送钻石数量
	config, err = s.configRepo.GetConfigByKey("default_vip_give_diamond", mysqlTx)
	if err != nil {
		s.bootstrap.Log.Error("获取VIP赠送钻石配置失败: %v", err)
		mysqlTx.Rollback()
		return
	}
	var defaultVipGiveDiamond dto.DefaultVipGiveDiamondDto
	if err := json.Unmarshal([]byte(config.ConfigValue), &defaultVipGiveDiamond); err != nil {
		s.bootstrap.Log.Error("解析VIP赠送钻石配置失败: %v", err)
		mysqlTx.Rollback()
		return
	}

	var monthlyDiamond uint64
	for _, vip := range giveVip {
		// 如果VIP有设置每月赠送钻石数量, 则使用VIP设置的钻石数量
		monthlyDiamond = uint64(vip.GiveNum)
		if monthlyDiamond == 0 {
			switch vip.VipType {
			case model.VipTypeMonth:
				monthlyDiamond = defaultVipGiveDiamond.MonthGive
			case model.VipTypeQuarter:
				monthlyDiamond = defaultVipGiveDiamond.QuarterGive
			case model.VipTypeYear:
				monthlyDiamond = defaultVipGiveDiamond.YearGive
			}
		}

		// 赠送钻石
		if err := s.userService.AddOrSubUserDiamondAndAddRecord(service.DiamondOperationRequest{
			UserID:  vip.UserId,
			OrderID: vip.OrderId,
			GoodsID: vip.GoodsId,
			Diamond: monthlyDiamond,
			Mark:    "会员每月赠送钻石",
			Op:      int(model.StatusEnabled),
		}, mysqlTx); err != nil {
			s.bootstrap.Log.Error("赠送钻石失败: %v", err.Error())
			mysqlTx.Rollback()
			return
		}
	}

	// 批量更新这些赠送记录的状态为已赠送和已删除
	if err := s.userVipRepo.BatchUpdateVipGiveStatusAndIsDelete(giveVip, mysqlTx); err != nil {
		s.bootstrap.Log.Error("批量更新VIP赠送记录状态失败: %v", err)
		mysqlTx.Rollback()
		return
	}

	if err := mysqlTx.Commit().Error; err != nil {
		s.bootstrap.Log.Error("提交事务失败: %v", err)
		mysqlTx.Rollback()
		return
	}
	s.bootstrap.Log.Info("已成功向%d位会员用户赠送每月钻石", len(giveVip))
}
