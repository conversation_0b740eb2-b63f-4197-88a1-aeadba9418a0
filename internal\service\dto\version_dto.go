package dto

import (
	"chongli/internal/model"
	"time"
)

// VersionDto 版本数据传输对象
type VersionDto struct {
	ID          int64            `json:"id"`                         // 主键id
	Version     string           `json:"version" binding:"required"` // 版本
	Desc        string           `json:"desc"`                       // 版本内容
	IsForce     model.StatusFlag `json:"is_force"`                   // 是否强制更新：0否，1是
	Channel     string           `json:"channel"`                    // 客户端/渠道
	CreateAt    time.Time        `json:"create_at"`                  // 创建时间
	IsDelete    model.StatusFlag `json:"is_delete"`                  // 删除标记：-1未删除，1删除
	DownloadUrl string           `json:"download_url"`               // 下载地址
	VersionInt  int64            `json:"version_int"`                // 版本int值
	UpdateAt    time.Time        `json:"update_at"`                  // 更新时间
	IsRelease   model.StatusFlag `json:"is_release"`                 // 是否发布
}

// VersionQueryRequest 通用查询请求（用于Select、List、Count等查询操作）
type VersionQueryRequest struct {
	VersionDto
	OrderBy string `json:"order_by,omitempty"`
}

// VersionPageQueryRequest 版本分页查询请求
type VersionPageQueryRequest struct {
	VersionQueryRequest
	Page int `json:"page" binding:"required"`
	Size int `json:"size" binding:"required"`
}

// VersionIDRequest 通用ID请求（用于Release、CancelRelease等ID操作）
type VersionIDRequest struct {
	ID int64 `json:"id" binding:"required"`
}
