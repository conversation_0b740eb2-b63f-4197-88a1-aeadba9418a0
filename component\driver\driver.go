package driver

import (
	"sync"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// driverContainer singleton of Container.
var driverContainer IDriverContainer

// IDriverContainer interfaces export to repository layer.
type IDriverContainer interface {
	GetClickHouseDriver() IClickHouseDriver
	GetRdsClient() *redis.Client
	GetMysqlDb() *gorm.DB
	GetPgSQLDb() *gorm.DB
	GetSQLServerDb() *gorm.DB
	GetEsClient() (ec IESClient, err error)
}

// Container struct of Container.
type Container struct {
}

// InitDrivers init the drivers which you need.
func InitDrivers() IDriverContainer {
	// init ClickHouse
	// _ = initClickHouse()
	// init Redis
	_ = initRdsClient()
	// init MySQL
	_ = initMysqlDb()
	// init PostgreSQL
	//_ = initPgSQLDb()
	// init SqlServer
	// _ = initSqlServer()
	// init elastic-search
	//_ = initEsClient()

	driverContainer = newDriverContainer()
	return driverContainer
}

// NewDriverContainer new Container.
func newDriverContainer() IDriverContainer {
	return &Container{}
}

// GetDriverContainer get a singleton of Container.
func GetDriverContainer() IDriverContainer {
	if driverContainer == nil {
		// execution once.
		var once sync.Once
		once.Do(func() {
			driverContainer = newDriverContainer()
		})
	}
	return driverContainer
}

// GetClickHouseDriver get ClickHouse driver.
func (d *Container) GetClickHouseDriver() IClickHouseDriver {
	return getClickHouseDriver()
}

// GetRdsClient get a Redis client.
func (d *Container) GetRdsClient() *redis.Client {
	return getRdsClient()
}

// GetMysqlDb get a MySQL db.
func (d *Container) GetMysqlDb() *gorm.DB {
	return getMysqlDb()
}

// GetPgSQLDb get a PostgreSQL db.
func (d *Container) GetPgSQLDb() *gorm.DB {
	return getPgSQLDb()
}

// GetSQLServerDb get a SQLServer db.
func (d *Container) GetSQLServerDb() *gorm.DB {
	return getSQLServerDb()
}

// GetEsClient get es client.
func (d *Container) GetEsClient() (ec IESClient, err error) {
	return getEsClient()
}
