package httpclient

import (
	"errors"
	"time"

	"github.com/valyala/fasthttp"
)

// HttpGet send http get request.
func HttpGet(uri string, queryStr string, header map[string]string, timeout time.Duration) (responseStr string, err error) {
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	if uri == "" {
		err = errors.New("uri is empty")
		return
	}
	req.SetRequestURI(uri)
	if queryStr != "" {
		req.URI().SetQueryString(queryStr)
	}
	if len(header) > 0 {
		for _key, _val := range header {
			req.Header.Set(_key, _val)
		}
	}

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)
	fasthttpClient := &fasthttp.Client{
		MaxIdleConnDuration: timeout,
		MaxConnDuration:     timeout,
		ReadTimeout:         timeout,
		WriteTimeout:        timeout,
		MaxConnWaitTimeout:  timeout,
	}
	if err = fasthttpClient.DoTimeout(req, resp, fasthttpClient.ReadTimeout); err != nil {
		return
	}

	responseStr = string(resp.Body())

	return
}

// DefaultHeader default headers.
var DefaultHeader = map[string]string{
	"Accept-Encoding": "gzip",
}

// HttpPost send http post request.
func HttpPost(uri string, params string, body string, header map[string]string, timeout time.Duration) (responseStr string, err error) {
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	req.Header.SetMethod("POST")

	if uri == "" {
		err = errors.New("uri is empty")
		return
	}
	req.SetRequestURI(uri)

	if params != "" {
		req.URI().SetQueryString(params)
	}
	req.Header.SetContentType("application/json;charset=utf8")
	if len(header) > 0 {
		for _key, _val := range header {
			req.Header.Set(_key, _val)
		}
	}
	requestBody := []byte(body)
	req.SetBody(requestBody)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)
	if timeout == 0 {
		timeout = 3 * time.Second
	}
	fasthttpClient := &fasthttp.Client{
		MaxIdleConnDuration: timeout,
		MaxConnDuration:     timeout,
		ReadTimeout:         timeout,
		WriteTimeout:        timeout,
		MaxConnWaitTimeout:  timeout,
	}
	if err = fasthttpClient.DoTimeout(req, resp, fasthttpClient.ReadTimeout); err != nil {
		return
	}

	responseStr = string(resp.Body())

	return
}
