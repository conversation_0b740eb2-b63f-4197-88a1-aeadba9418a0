package router

import (
	"chongli/internal/app/api/controller"
	"chongli/internal/middleware"
	"github.com/gin-gonic/gin"
)

type UserRouter struct {
	userLoginCtrl     *controller.UserLoginController
	userInfoCtrl      *controller.UserInfoController
	userGiveCtrl      *controller.UserGiveController
	JwtAuthMiddleware *middleware.JWTAuthMiddleware
}

func NewUserRouter(
	engine *gin.Engine,
	userLoginCtrl *controller.UserLoginController,
	userInfoCtrl *controller.UserInfoController,
	userGiveCtrl *controller.UserGiveController,
	JwtAuthMiddleware *middleware.JWTAuthMiddleware,
) *UserRouter {
	router := &UserRouter{
		userLoginCtrl:     userLoginCtrl,
		userInfoCtrl:      userInfoCtrl,
		userGiveCtrl:      userGiveCtrl,
		JwtAuthMiddleware: JwtAuthMiddleware,
	}
	user := engine.Group("api/user/login")
	router.initLogin(user)
	userInfo := engine.Group("api/user/info")
	router.initUserInfo(userInfo)
	userGive := engine.Group("api/user/give")
	router.initUserGive(userGive)
	return router
}

func (r *UserRouter) initLogin(user *gin.RouterGroup) {
	// 一键登录
	user.POST("phone", r.userLoginCtrl.OneClickLogin)
	// 发送验证码
	user.POST("send_code", r.userLoginCtrl.SendCode)
	// 验证码登录
	user.POST("code", r.userLoginCtrl.PhoneCodeLogin)
	// 苹果登录
	user.GET("apple", r.userLoginCtrl.AppleLogin)
}

func (r *UserRouter) initUserInfo(userInfo *gin.RouterGroup) {
	userInfo.Use(r.JwtAuthMiddleware.JWTAuth())
	// 获取用户信息
	userInfo.GET("", r.userInfoCtrl.GetUserInfo)
	// 获取用户钻石记录
	userInfo.GET("diamond/record", r.userInfoCtrl.GetUserDiamondRecord)
	// 用户注销
	userInfo.POST("cancel", r.userInfoCtrl.CancelUser)
	// 用户退出登录
	userInfo.POST("logout", r.userInfoCtrl.Logout)
}

func (r *UserRouter) initUserGive(userGive *gin.RouterGroup) {
	userGive.Use(r.JwtAuthMiddleware.JWTAuth())
	// 赠送每日免费钻石
	userGive.GET("diamond", r.userGiveCtrl.GiveEveryDayFreeDiamond)
}
