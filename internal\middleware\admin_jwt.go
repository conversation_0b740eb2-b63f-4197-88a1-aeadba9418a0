package middleware

import (
	"chongli/component/apollo"
	"chongli/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// AdminJWTAuth admin JWT authentication middleware
func AdminJWTAuth() gin.HandlerFunc {
	claimKey := "admin_claim"
	return func(c *gin.Context) {
		token := c.Request.Header.Get("Authorization")
		if token == "" {
			// 也支持从X-Token头部获取
			token = c.Request.Header.Get("X-Token")
		}
		if token == "" {
			// 也支持从Cookie获取
			token, _ = c.<PERSON>("token")
		}

		// 移除Bearer前缀
		if len(token) > 7 && token[:7] == "Bearer " {
			token = token[7:]
		}

		if token == "" {
			c.JSON(401, gin.H{
				"code":    401,
				"message": "未提供认证token",
			})
			c.Abort()
			return
		}

		jwtSecret := apollo.GetApolloConfig().JwtSecret
		j := jwt.NewJWT(jwtSecret)
		claims, errParse := j.<PERSON>(token)
		if errParse != nil {
			// token过期
			if errParse.Error() == jwt.ValidationErrorExpired {
				c.JSON(401, gin.H{
					"code":    401,
					"message": "token已过期",
				})
				c.Abort()
				return
			}
			c.JSON(401, gin.H{
				"code":    401,
				"message": "token无效: " + errParse.Error(),
			})
			c.Abort()
			return
		}

		// 验证是否是admin用户
		meta := claims.MetaData
		if userType, ok := meta["user_type"]; !ok || userType != "admin" {
			c.JSON(403, gin.H{
				"code":    403,
				"message": "无权限访问",
			})
			c.Abort()
			return
		}

		// 将用户信息设置到context
		if userID, ok := meta["user_id"]; ok {
			c.Set("admin_user_id", userID)
		}
		if email, ok := meta["email"]; ok {
			c.Set("admin_email", email)
		}
		c.Set(claimKey, claims)

		c.Next()
	}
}
