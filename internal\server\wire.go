//go:build wireinject
// +build wireinject

package server

import (
	"chongli/component"
	"chongli/internal/app/admin"
	"chongli/internal/app/api"
	"chongli/internal/app/task"
	"chongli/internal/dao"
	"chongli/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/google/wire"
	"github.com/robfig/cron/v3"
)

func initRouter(engine *gin.Engine, bootstrap *component.BootStrap) AppRouter {
	panic(
		wire.Build(
			// 公共依赖
			dao.ProviderSet,
			service.Set,
			// API 模块
			api.MiddlewareSet,
			api.RouterSet,
			api.ControllerSet,
			api.ServiceSet,
			// Admin 模块
			admin.RouterSet,
			admin.ControllerSet,
			makeRouter,
			wire.Struct(new(Router), "*"),
		),
	)
}

func initTask(c *cron.Cron, bootstrap *component.BootStrap) *TimerTask {
	panic(
		wire.Build(
			// 公共依赖（只定义一次，避免重复绑定）
			dao.ProviderSet, service.Set,
			task.Set,
			wire.Struct(new(Task), "*"),
			wire.Struct(new(TimerTask), "*"),
		),
	)
}
