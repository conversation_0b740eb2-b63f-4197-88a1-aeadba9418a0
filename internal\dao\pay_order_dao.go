package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"time"

	"gorm.io/gorm"
)

type payOrderRepo struct {
	log *logger.Logger
	db  *gorm.DB
}

func NewPayOrderRepo(bootStrap *component.BootStrap) repo.PayOrderRepo {
	return &payOrderRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
	}
}

func (d *payOrderRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// convertModel2Dto 将模型转换为DTO
func (d *payOrderRepo) convertModel2Dto(order *model.PayOrder) *dto.PayOrderDto {
	return &dto.PayOrderDto{
		ID:                  order.ID,
		OrderID:             order.OrderID,
		WnlOrderID:          order.WnlOrderID,
		OrderState:          order.OrderState,
		OrderStateText:      order.GetOrderStateText(),
		GoodsID:             order.GoodsID,
		GoodsMiddleID:       order.GoodsMiddleID,
		GoodsTitle:          order.GoodsTitle,
		UserID:              order.UserID,
		UserDeviceID:        order.UserDeviceID,
		UserBindChannelID:   order.UserBindChannelID,
		UserBindChannelName: order.UserBindChannelName,
		UserIP:              order.UserIP,
		PayType:             order.PayType,
		PayTypeText:         order.GetPayTypeText(),
		OrderAmount:         order.OrderAmount,
		PayAmount:           order.PayAmount,
		RefundAmount:        order.RefundAmount,
		Version:             order.Version,
		Channel:             order.Channel,
		PayAt:               order.PayAt,
		RefundAt:            order.RefundAt,
		ExpireAt:            order.ExpireAt,
		CreateAt:            order.CreateAt,
		UpdateAt:            order.UpdateAt,
		WnlCallbackData:     order.WnlCallbackData,
		Remark:              order.Remark,
		IsDelete:            order.IsDelete,
	}
}

// convertDto2Model 将DTO转换为模型
func (d *payOrderRepo) convertDto2Model(dto *dto.PayOrderDto) *model.PayOrder {
	return &model.PayOrder{
		ID:                  dto.ID,
		OrderID:             dto.OrderID,
		WnlOrderID:          dto.WnlOrderID,
		OrderState:          dto.OrderState,
		GoodsID:             dto.GoodsID,
		GoodsMiddleID:       dto.GoodsMiddleID,
		GoodsTitle:          dto.GoodsTitle,
		UserID:              dto.UserID,
		UserDeviceID:        dto.UserDeviceID,
		UserBindChannelID:   dto.UserBindChannelID,
		UserBindChannelName: dto.UserBindChannelName,
		UserIP:              dto.UserIP,
		PayType:             dto.PayType,
		OrderAmount:         dto.OrderAmount,
		PayAmount:           dto.PayAmount,
		RefundAmount:        dto.RefundAmount,
		Version:             dto.Version,
		Channel:             dto.Channel,
		PayAt:               dto.PayAt,
		RefundAt:            dto.RefundAt,
		ExpireAt:            dto.ExpireAt,
		CreateAt:            dto.CreateAt,
		UpdateAt:            dto.UpdateAt,
		WnlCallbackData:     dto.WnlCallbackData,
		Remark:              dto.Remark,
		IsDelete:            dto.IsDelete,
	}
}

// CreatePayOrder 创建支付订单
func (d *payOrderRepo) CreatePayOrder(orderDto *dto.PayOrderDto, tx ...*gorm.DB) error {
	order := d.convertDto2Model(orderDto)
	if err := d.getDb(tx).Create(order).Error; err != nil {
		d.log.Error("创建支付订单错误: %v", err.Error())
		return err
	}
	// 更新DTO中的ID
	orderDto.ID = order.ID
	return nil
}

// GetPayOrderByID 根据ID获取支付订单
func (d *payOrderRepo) GetPayOrderByID(id int64, tx ...*gorm.DB) (*dto.PayOrderDto, error) {
	var order model.PayOrder
	if err := d.getDb(tx).Where("id = ? AND is_delete = ?", id, model.NotDeleted).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据ID获取支付订单错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&order), nil
}

// GetPayOrderByOrderID 根据订单ID获取支付订单
func (d *payOrderRepo) GetPayOrderByOrderID(orderID string, tx ...*gorm.DB) (*dto.PayOrderDto, error) {
	var order model.PayOrder
	if err := d.getDb(tx).Where("order_id = ? AND is_delete = ?", orderID, model.NotDeleted).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据订单ID获取支付订单错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&order), nil
}

// GetPayOrderByWnlOrderID 根据万年历订单ID获取支付订单
func (d *payOrderRepo) GetPayOrderByWnlOrderID(wnlOrderID string, tx ...*gorm.DB) (*dto.PayOrderDto, error) {
	var order model.PayOrder
	if err := d.getDb(tx).Where("wnl_order_id = ? AND is_delete = ?", wnlOrderID, model.NotDeleted).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据万年历订单ID获取支付订单错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&order), nil
}

// UpdatePayOrder 更新支付订单
func (d *payOrderRepo) UpdatePayOrder(orderDto *dto.PayOrderDto, tx ...*gorm.DB) error {
	order := d.convertDto2Model(orderDto)
	if err := d.getDb(tx).Save(order).Error; err != nil {
		d.log.Error("更新支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// UpdatePayOrderByID 根据ID更新支付订单
func (d *payOrderRepo) UpdatePayOrderByID(id int64, fields map[string]any, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("id = ? AND is_delete = ?", id, model.NotDeleted).Updates(fields).Error; err != nil {
		d.log.Error("根据ID更新支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// UpdatePayOrderByOrderID 根据订单ID更新支付订单
func (d *payOrderRepo) UpdatePayOrderByOrderID(orderID string, fields map[string]any, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_id = ? AND is_delete = ?", orderID, model.NotDeleted).Updates(fields).Error; err != nil {
		d.log.Error("根据订单ID更新支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// DeletePayOrder 删除支付订单（软删除）
func (d *payOrderRepo) DeletePayOrder(id int64, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("id = ?", id).Update("is_delete", model.Deleted).Error; err != nil {
		d.log.Error("删除支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// DeletePayOrderByOrderID 根据订单ID删除支付订单（软删除）
func (d *payOrderRepo) DeletePayOrderByOrderID(orderID string, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_id = ?", orderID).Update("is_delete", model.Deleted).Error; err != nil {
		d.log.Error("根据订单ID删除支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// GetPayOrderList 获取支付订单列表
func (d *payOrderRepo) GetPayOrderList(req *dto.GetPayOrderListRequest) ([]*dto.PayOrderDto, int64, error) {
	var orders []*model.PayOrder
	var total int64

	query := d.db.Model(&model.PayOrder{})

	// 构建查询条件
	if req.OrderID != "" {
		query = query.Where("order_id LIKE ?", "%"+req.OrderID+"%")
	}
	if req.WnlOrderID != "" {
		query = query.Where("wnl_order_id LIKE ?", "%"+req.WnlOrderID+"%")
	}
	if req.OrderState != nil && *req.OrderState != 0 {
		query = query.Where("order_state = ?", *req.OrderState)
	}
	if req.GoodsID != 0 {
		query = query.Where("goods_id = ?", req.GoodsID)
	}
	if req.GoodsMiddleID != "" {
		query = query.Where("goods_middle_id = ?", req.GoodsMiddleID)
	}
	if req.UserID != nil && *req.UserID != 0 {
		query = query.Where("user_id = ?", *req.UserID)
	}
	if req.UserDeviceID != "" {
		query = query.Where("user_device_id = ?", req.UserDeviceID)
	}
	if req.UserBindChannelID != 0 {
		query = query.Where("user_bind_channel_id = ?", req.UserBindChannelID)
	}
	if req.UserBindChannelName != "" {
		query = query.Where("user_bind_channel_name = ?", req.UserBindChannelName)
	}
	if req.UserIP != "" {
		query = query.Where("user_ip = ?", req.UserIP)
	}
	if req.PayType != nil && *req.PayType != 0 {
		query = query.Where("pay_type = ?", *req.PayType)
	}
	if req.Version != "" {
		query = query.Where("version = ?", req.Version)
	}
	if req.Channel != "" {
		query = query.Where("channel = ?", req.Channel)
	}
	if req.IsDelete != nil && *req.IsDelete != 0 {
		query = query.Where("is_delete = ?", *req.IsDelete)
	}
	if req.BeginCreateAt != nil && !req.BeginCreateAt.IsZero() {
		query = query.Where("create_at >= ?", *req.BeginCreateAt)
	}
	if req.EndCreateAt != nil && !req.BeginCreateAt.IsZero() {
		query = query.Where("create_at <= ?", *req.EndCreateAt)
	}
	if req.BeginPayAt != nil && !req.BeginCreateAt.IsZero() {
		query = query.Where("pay_at >= ?", *req.BeginPayAt)
	}
	if req.EndPayAt != nil && !req.BeginCreateAt.IsZero() {
		query = query.Where("pay_at <= ?", *req.EndPayAt)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		d.log.Error("获取支付订单总数错误: %v", err.Error())
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("create_at DESC").Offset(offset).Limit(req.PageSize).Find(&orders).Error; err != nil {
		d.log.Error("获取支付订单列表错误: %v", err.Error())
		return nil, 0, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}

	return orderDtos, total, nil
}

// GetPayOrdersByUserID 根据用户ID获取支付订单列表
func (d *payOrderRepo) GetPayOrdersByUserID(userID int64, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("user_id = ? AND is_delete = ?", userID, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据用户ID获取支付订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetPayOrdersByGoodsID 根据商品ID获取支付订单列表
func (d *payOrderRepo) GetPayOrdersByGoodsID(goodsID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("goods_id = ? AND is_delete = ?", goodsID, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据商品ID获取支付订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetPayOrdersByState 根据订单状态获取支付订单列表
func (d *payOrderRepo) GetPayOrdersByState(state int, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("order_state = ? AND is_delete = ?", state, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据订单状态获取支付订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetExpiredOrders 获取过期订单列表
func (d *payOrderRepo) GetExpiredOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	now := time.Now()
	query := d.getDb(tx).Where("(order_state = ? OR expire_at < ?) AND is_delete = ?", model.OrderStateExpired, now, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("获取过期订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetPendingOrders 获取待支付订单列表
func (d *payOrderRepo) GetPendingOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	return d.GetPayOrdersByState(model.OrderStatePending, limit, tx...)
}

// GetPaidOrders 获取已支付订单列表
func (d *payOrderRepo) GetPaidOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	return d.GetPayOrdersByState(model.OrderStatePaid, limit, tx...)
}

// CountOrdersByUserID 统计用户订单数量
func (d *payOrderRepo) CountOrdersByUserID(userID int64, tx ...*gorm.DB) (int64, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("user_id = ? AND is_delete = ?", userID, model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("统计用户订单数量错误: %v", err.Error())
		return 0, err
	}
	return count, nil
}

// CountOrdersByState 统计指定状态的订单数量
func (d *payOrderRepo) CountOrdersByState(state int, tx ...*gorm.DB) (int64, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_state = ? AND is_delete = ?", state, model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("统计指定状态的订单数量错误: %v", err.Error())
		return 0, err
	}
	return count, nil
}

// CountOrdersByPayType 统计指定支付方式的订单数量
func (d *payOrderRepo) CountOrdersByPayType(payType int, tx ...*gorm.DB) (int64, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("pay_type = ? AND is_delete = ?", payType, model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("统计指定支付方式的订单数量错误: %v", err.Error())
		return 0, err
	}
	return count, nil
}

// GetOrderAmountSumByUserID 获取用户订单总金额
func (d *payOrderRepo) GetOrderAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error) {
	var sum float64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("user_id = ? AND is_delete = ?", userID, model.NotDeleted).Select("COALESCE(SUM(order_amount), 0)").Scan(&sum).Error; err != nil {
		d.log.Error("获取用户订单总金额错误: %v", err.Error())
		return 0, err
	}
	return sum, nil
}

// GetPayAmountSumByUserID 获取用户实际支付总金额
func (d *payOrderRepo) GetPayAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error) {
	var sum float64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("user_id = ? AND order_state = ? AND is_delete = ?", userID, model.OrderStatePaid, model.NotDeleted).Select("COALESCE(SUM(pay_amount), 0)").Scan(&sum).Error; err != nil {
		d.log.Error("获取用户实际支付总金额错误: %v", err.Error())
		return 0, err
	}
	return sum, nil
}

// GetRefundAmountSumByUserID 获取用户退款总金额
func (d *payOrderRepo) GetRefundAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error) {
	var sum float64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("user_id = ? AND order_state = ? AND is_delete = ?", userID, model.OrderStateRefund, model.NotDeleted).Select("COALESCE(SUM(refund_amount), 0)").Scan(&sum).Error; err != nil {
		d.log.Error("获取用户退款总金额错误: %v", err.Error())
		return 0, err
	}
	return sum, nil
}

// BatchUpdateOrderState 批量更新订单状态
func (d *payOrderRepo) BatchUpdateOrderState(orderIDs []string, state int, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_id IN ? AND is_delete = ?", orderIDs, model.NotDeleted).Update("order_state", state).Error; err != nil {
		d.log.Error("批量更新订单状态错误: %v", err.Error())
		return err
	}
	return nil
}

// GetOrdersByChannel 根据渠道获取订单列表
func (d *payOrderRepo) GetOrdersByChannel(channel string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("channel = ? AND is_delete = ?", channel, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据渠道获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetOrdersByVersion 根据版本获取订单列表
func (d *payOrderRepo) GetOrdersByVersion(version string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("version = ? AND is_delete = ?", version, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据版本获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetOrdersByUserIP 根据用户IP获取订单列表
func (d *payOrderRepo) GetOrdersByUserIP(userIP string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("user_ip = ? AND is_delete = ?", userIP, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据用户IP获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetOrdersByUserDeviceID 根据用户设备ID获取订单列表
func (d *payOrderRepo) GetOrdersByUserDeviceID(userDeviceID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("user_device_id = ? AND is_delete = ?", userDeviceID, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据用户设备ID获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetOrdersByUserBindChannelID 根据用户绑定渠道ID获取订单列表
func (d *payOrderRepo) GetOrdersByUserBindChannelID(userBindChannelID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("user_bind_channel_id = ? AND is_delete = ?", userBindChannelID, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据用户绑定渠道ID获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// IsOrderExists 检查订单是否存在
func (d *payOrderRepo) IsOrderExists(orderID string, tx ...*gorm.DB) (bool, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).
		Where("order_id = ? AND is_delete = ?", orderID, model.NotDeleted).
		Count(&count).Error; err != nil {
		d.log.Error("检查订单是否存在错误: %v", err.Error())
		return false, err
	}
	return count > 0, nil
}

// IsWnlOrderExists 检查万年历订单是否存在
func (d *payOrderRepo) IsWnlOrderExists(wnlOrderID string, tx ...*gorm.DB) (bool, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("wnl_order_id = ? AND is_delete = ?", wnlOrderID, model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("检查万年历订单是否存在错误: %v", err.Error())
		return false, err
	}
	return count > 0, nil
}

// GetExpiredUnpaidOrders 获取过期未支付订单列表
func (d *payOrderRepo) GetExpiredUnpaidOrders(tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	now := time.Now()
	query := d.getDb(tx).Where("order_state = ? AND expire_at < ? AND is_delete = ?", model.OrderStatePending, now, model.NotDeleted).Order("create_at DESC")

	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("获取过期未支付订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// BatchUpdateOrderStateAndRemark 批量更新订单状态和备注
func (d *payOrderRepo) BatchUpdateOrderStateAndRemark(orderIDs []string, state int, remark string, tx ...*gorm.DB) error {
	if len(orderIDs) == 0 {
		return nil
	}

	updates := map[string]any{
		"order_state": state,
		"remark":      remark,
		"update_at":   time.Now(),
	}

	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_id IN ? AND is_delete = ?", orderIDs, model.NotDeleted).Updates(updates).Error; err != nil {
		d.log.Error("批量更新订单状态和备注错误: %v", err.Error())
		return err
	}
	return nil
}

// GetOrderStatistics 获取订单统计数据
func (d *payOrderRepo) GetOrderStatistics() (*dto.OrderStatisticsDto, error) {
	stats := &dto.OrderStatisticsDto{}

	// 获取总订单量
	totalOrders, err := d.GetTotalOrdersCount()
	if err != nil {
		return nil, err
	}
	stats.TotalOrders = totalOrders

	// 获取各渠道总订单量
	totalOrdersByChannel, err := d.GetTotalOrdersByChannel()
	if err != nil {
		return nil, err
	}
	stats.TotalOrdersByChannel = totalOrdersByChannel

	// 获取总已支付金额（排除沙盒）
	totalPaidAmount, err := d.GetTotalPaidAmount()
	if err != nil {
		return nil, err
	}
	stats.TotalPaidAmount = totalPaidAmount

	// 获取各渠道总已支付金额
	totalPaidAmountByChannel, err := d.GetTotalPaidAmountByChannel()
	if err != nil {
		return nil, err
	}
	stats.TotalPaidAmountByChannel = totalPaidAmountByChannel

	// 获取总退款金额（排除沙盒）
	totalRefundAmount, err := d.GetTotalRefundAmount()
	if err != nil {
		return nil, err
	}
	stats.TotalRefundAmount = totalRefundAmount

	// 获取各渠道总退款金额
	totalRefundAmountByChannel, err := d.GetTotalRefundAmountByChannel()
	if err != nil {
		return nil, err
	}
	stats.TotalRefundAmountByChannel = totalRefundAmountByChannel

	// 获取总已支付订单量（排除沙盒）
	totalPaidOrders, err := d.GetTotalPaidOrdersCount()
	if err != nil {
		return nil, err
	}
	stats.TotalPaidOrders = totalPaidOrders

	// 获取各渠道总已支付订单量
	totalPaidOrdersByChannel, err := d.GetTotalPaidOrdersByChannel()
	if err != nil {
		return nil, err
	}
	stats.TotalPaidOrdersByChannel = totalPaidOrdersByChannel

	// 获取当前年份的月度已支付订单统计
	currentYear := time.Now().Year()
	monthlyPaidOrderStats, err := d.GetMonthlyPaidOrderStats(currentYear)
	if err != nil {
		return nil, err
	}
	stats.MonthlyPaidOrderStats = monthlyPaidOrderStats

	// 获取昨日新增订单量
	yesterdayNewOrders, err := d.GetYesterdayNewOrdersCount()
	if err != nil {
		return nil, err
	}
	stats.YesterdayNewOrders = yesterdayNewOrders

	// 获取昨日各渠道新增订单
	yesterdayNewOrdersByChannel, err := d.GetYesterdayNewOrdersByChannel()
	if err != nil {
		return nil, err
	}
	stats.YesterdayNewOrdersByChannel = yesterdayNewOrdersByChannel

	// 获取昨日新增已支付订单量（排除沙盒）
	yesterdayNewPaidOrders, err := d.GetYesterdayNewPaidOrdersCount()
	if err != nil {
		return nil, err
	}
	stats.YesterdayNewPaidOrders = yesterdayNewPaidOrders

	// 获取昨日各渠道新增已支付订单
	yesterdayNewPaidOrdersByChannel, err := d.GetYesterdayNewPaidOrdersByChannel()
	if err != nil {
		return nil, err
	}
	stats.YesterdayNewPaidOrdersByChannel = yesterdayNewPaidOrdersByChannel

	// 获取本月新增订单量
	thisMonthNewOrders, err := d.GetThisMonthNewOrdersCount()
	if err != nil {
		return nil, err
	}
	stats.ThisMonthNewOrders = thisMonthNewOrders

	// 获取本月各渠道新增订单
	thisMonthNewOrdersByChannel, err := d.GetThisMonthNewOrdersByChannel()
	if err != nil {
		return nil, err
	}
	stats.ThisMonthNewOrdersByChannel = thisMonthNewOrdersByChannel

	// 获取本月新增已支付订单量（排除沙盒）
	thisMonthNewPaidOrders, err := d.GetThisMonthNewPaidOrdersCount()
	if err != nil {
		return nil, err
	}
	stats.ThisMonthNewPaidOrders = thisMonthNewPaidOrders

	// 获取本月各渠道新增已支付订单
	thisMonthNewPaidOrdersByChannel, err := d.GetThisMonthNewPaidOrdersByChannel()
	if err != nil {
		return nil, err
	}
	stats.ThisMonthNewPaidOrdersByChannel = thisMonthNewPaidOrdersByChannel

	// 获取本日新增订单量
	todayNewOrders, err := d.GetTodayNewOrdersCount()
	if err != nil {
		return nil, err
	}
	stats.TodayNewOrders = todayNewOrders

	// 获取本日各渠道新增订单
	todayNewOrdersByChannel, err := d.GetTodayNewOrdersByChannel()
	if err != nil {
		return nil, err
	}
	stats.TodayNewOrdersByChannel = todayNewOrdersByChannel

	// 获取本日新增已支付订单量（排除沙盒）
	todayNewPaidOrders, err := d.GetTodayNewPaidOrdersCount()
	if err != nil {
		return nil, err
	}
	stats.TodayNewPaidOrders = todayNewPaidOrders

	// 获取本日各渠道新增已支付订单
	todayNewPaidOrdersByChannel, err := d.GetTodayNewPaidOrdersByChannel()
	if err != nil {
		return nil, err
	}
	stats.TodayNewPaidOrdersByChannel = todayNewPaidOrdersByChannel

	// 获取本月每日新增已支付订单统计
	thisMonthDailyNewPaidOrders, err := d.GetThisMonthDailyNewPaidOrders()
	if err != nil {
		return nil, err
	}
	stats.ThisMonthDailyNewPaidOrders = thisMonthDailyNewPaidOrders

	return stats, nil
}

// GetTotalOrdersCount 获取总订单量
func (d *payOrderRepo) GetTotalOrdersCount() (int64, error) {
	var count int64
	if err := d.db.Model(&model.PayOrder{}).Where("is_delete = ?", model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("获取总订单量失败: %v", err)
		return 0, err
	}
	return count, nil
}

// GetTotalOrdersByChannel 获取各渠道总订单量
func (d *payOrderRepo) GetTotalOrdersByChannel() ([]dto.ChannelOrderCountDto, error) {
	type ChannelCount struct {
		ChannelID   int64  `gorm:"column:user_bind_channel_id"`
		ChannelName string `gorm:"column:user_bind_channel_name"`
		OrderCount  int64  `gorm:"column:order_count"`
	}

	var results []ChannelCount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
		Where("is_delete = ?", model.NotDeleted).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("order_count DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取各渠道总订单量失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelCounts []dto.ChannelOrderCountDto
	for _, result := range results {
		channelCounts = append(channelCounts, dto.ChannelOrderCountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			OrderCount:  result.OrderCount,
		})
	}

	return channelCounts, nil
}

// GetTotalPaidAmount 获取总已支付金额（排除沙盒）
func (d *payOrderRepo) GetTotalPaidAmount() (float64, error) {
	var totalAmount float64
	if err := d.db.Model(&model.PayOrder{}).
		Select("COALESCE(SUM(pay_amount), 0)").
		Where("order_state = ? AND pay_type != ? AND is_delete = ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted).
		Scan(&totalAmount).Error; err != nil {
		d.log.Error("获取总已支付金额失败: %v", err)
		return 0, err
	}
	return totalAmount, nil
}

// GetTotalPaidAmountByChannel 获取各渠道总已支付金额（排除沙盒）
func (d *payOrderRepo) GetTotalPaidAmountByChannel() ([]dto.ChannelOrderAmountDto, error) {
	type ChannelAmount struct {
		ChannelID   int64   `gorm:"column:user_bind_channel_id"`
		ChannelName string  `gorm:"column:user_bind_channel_name"`
		Amount      float64 `gorm:"column:amount"`
	}

	var results []ChannelAmount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COALESCE(SUM(pay_amount), 0) as amount").
		Where("order_state = ? AND pay_type != ? AND is_delete = ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("amount DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取各渠道总已支付金额失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelAmounts []dto.ChannelOrderAmountDto
	for _, result := range results {
		channelAmounts = append(channelAmounts, dto.ChannelOrderAmountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			Amount:      result.Amount,
		})
	}

	return channelAmounts, nil
}

// GetTotalRefundAmount 获取总退款金额（排除沙盒）
func (d *payOrderRepo) GetTotalRefundAmount() (float64, error) {
	var totalAmount float64
	if err := d.db.Model(&model.PayOrder{}).
		Select("COALESCE(SUM(refund_amount), 0)").
		Where("order_state = ? AND pay_type != ? AND is_delete = ?",
			model.OrderStateRefund, model.PayTypeSandbox, model.NotDeleted).
		Scan(&totalAmount).Error; err != nil {
		d.log.Error("获取总退款金额失败: %v", err)
		return 0, err
	}
	return totalAmount, nil
}

// GetTotalRefundAmountByChannel 获取各渠道总退款金额（排除沙盒）
func (d *payOrderRepo) GetTotalRefundAmountByChannel() ([]dto.ChannelOrderAmountDto, error) {
	type ChannelAmount struct {
		ChannelID   int64   `gorm:"column:user_bind_channel_id"`
		ChannelName string  `gorm:"column:user_bind_channel_name"`
		Amount      float64 `gorm:"column:amount"`
	}

	var results []ChannelAmount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COALESCE(SUM(refund_amount), 0) as amount").
		Where("order_state = ? AND pay_type != ? AND is_delete = ?",
			model.OrderStateRefund, model.PayTypeSandbox, model.NotDeleted).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("amount DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取各渠道总退款金额失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelAmounts []dto.ChannelOrderAmountDto
	for _, result := range results {
		channelAmounts = append(channelAmounts, dto.ChannelOrderAmountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			Amount:      result.Amount,
		})
	}

	return channelAmounts, nil
}

// GetTotalPaidOrdersCount 获取总已支付订单量（排除沙盒）
func (d *payOrderRepo) GetTotalPaidOrdersCount() (int64, error) {
	var count int64
	if err := d.db.Model(&model.PayOrder{}).
		Where("order_state = ? AND pay_type != ? AND is_delete = ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted).
		Count(&count).Error; err != nil {
		d.log.Error("获取总已支付订单量失败: %v", err)
		return 0, err
	}
	return count, nil
}

// GetTotalPaidOrdersByChannel 获取各渠道总已支付订单量（排除沙盒）
func (d *payOrderRepo) GetTotalPaidOrdersByChannel() ([]dto.ChannelOrderCountDto, error) {
	type ChannelCount struct {
		ChannelID   int64  `gorm:"column:user_bind_channel_id"`
		ChannelName string `gorm:"column:user_bind_channel_name"`
		OrderCount  int64  `gorm:"column:order_count"`
	}

	var results []ChannelCount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
		Where("order_state = ? AND pay_type != ? AND is_delete = ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("order_count DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取各渠道总已支付订单量失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelCounts []dto.ChannelOrderCountDto
	for _, result := range results {
		channelCounts = append(channelCounts, dto.ChannelOrderCountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			OrderCount:  result.OrderCount,
		})
	}

	return channelCounts, nil
}

// GetMonthlyPaidOrderStats 获取每月已支付订单统计（当前年份1月到当前月）
func (d *payOrderRepo) GetMonthlyPaidOrderStats(year int) ([]dto.MonthlyOrderStatsDto, error) {
	currentMonth := int(time.Now().Month())
	var monthlyStats []dto.MonthlyOrderStatsDto

	for month := 1; month <= currentMonth; month++ {
		// 计算月份的开始和结束时间
		startTime := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
		endTime := startTime.AddDate(0, 1, 0).Add(-time.Second)

		// 获取该月总已支付订单量
		var totalPaidOrders int64
		if err := d.db.Model(&model.PayOrder{}).
			Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
				model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime, endTime).
			Count(&totalPaidOrders).Error; err != nil {
			d.log.Error("获取月度已支付订单量失败: %v", err)
			return nil, err
		}

		// 获取该月总已支付金额
		var totalPaidAmount float64
		if err := d.db.Model(&model.PayOrder{}).
			Select("COALESCE(SUM(pay_amount), 0)").
			Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
				model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime, endTime).
			Scan(&totalPaidAmount).Error; err != nil {
			d.log.Error("获取月度已支付金额失败: %v", err)
			return nil, err
		}

		// 获取该月各渠道已支付订单量
		type ChannelMonthlyCount struct {
			ChannelID   int64  `gorm:"column:user_bind_channel_id"`
			ChannelName string `gorm:"column:user_bind_channel_name"`
			OrderCount  int64  `gorm:"column:order_count"`
		}

		var channelOrderResults []ChannelMonthlyCount
		if err := d.db.Model(&model.PayOrder{}).
			Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
			Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
				model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime, endTime).
			Group("user_bind_channel_id, user_bind_channel_name").
			Order("order_count DESC").
			Scan(&channelOrderResults).Error; err != nil {
			d.log.Error("获取月度各渠道已支付订单量失败: %v", err)
			return nil, err
		}

		// 获取该月各渠道已支付金额
		type ChannelMonthlyAmount struct {
			ChannelID   int64   `gorm:"column:user_bind_channel_id"`
			ChannelName string  `gorm:"column:user_bind_channel_name"`
			Amount      float64 `gorm:"column:amount"`
		}

		var channelAmountResults []ChannelMonthlyAmount
		if err := d.db.Model(&model.PayOrder{}).
			Select("user_bind_channel_id, user_bind_channel_name, COALESCE(SUM(pay_amount), 0) as amount").
			Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
				model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime, endTime).
			Group("user_bind_channel_id, user_bind_channel_name").
			Order("amount DESC").
			Scan(&channelAmountResults).Error; err != nil {
			d.log.Error("获取月度各渠道已支付金额失败: %v", err)
			return nil, err
		}

		// 转换为DTO
		var paidOrdersByChannel []dto.ChannelOrderCountDto
		for _, result := range channelOrderResults {
			paidOrdersByChannel = append(paidOrdersByChannel, dto.ChannelOrderCountDto{
				ChannelID:   result.ChannelID,
				ChannelName: result.ChannelName,
				OrderCount:  result.OrderCount,
			})
		}

		var paidAmountByChannel []dto.ChannelOrderAmountDto
		for _, result := range channelAmountResults {
			paidAmountByChannel = append(paidAmountByChannel, dto.ChannelOrderAmountDto{
				ChannelID:   result.ChannelID,
				ChannelName: result.ChannelName,
				Amount:      result.Amount,
			})
		}

		monthlyStats = append(monthlyStats, dto.MonthlyOrderStatsDto{
			Year:                year,
			Month:               month,
			TotalPaidOrders:     totalPaidOrders,
			TotalPaidAmount:     totalPaidAmount,
			PaidOrdersByChannel: paidOrdersByChannel,
			PaidAmountByChannel: paidAmountByChannel,
		})
	}

	return monthlyStats, nil
}

// GetYesterdayNewOrdersCount 获取昨日新增订单量
func (d *payOrderRepo) GetYesterdayNewOrdersCount() (int64, error) {
	// 计算昨日的开始和结束时间
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	startTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	var count int64
	if err := d.db.Model(&model.PayOrder{}).
		Where("is_delete = ? AND create_at >= ? AND create_at <= ?",
			model.NotDeleted, startTime, endTime).
		Count(&count).Error; err != nil {
		d.log.Error("获取昨日新增订单量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// GetYesterdayNewOrdersByChannel 获取昨日各渠道新增订单
func (d *payOrderRepo) GetYesterdayNewOrdersByChannel() ([]dto.ChannelOrderCountDto, error) {
	// 计算昨日的开始和结束时间
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	startTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	type ChannelCount struct {
		ChannelID   int64  `gorm:"column:user_bind_channel_id"`
		ChannelName string `gorm:"column:user_bind_channel_name"`
		OrderCount  int64  `gorm:"column:order_count"`
	}

	var results []ChannelCount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
		Where("is_delete = ? AND create_at >= ? AND create_at <= ?",
			model.NotDeleted, startTime, endTime).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("order_count DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取昨日各渠道新增订单失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelCounts []dto.ChannelOrderCountDto
	for _, result := range results {
		channelCounts = append(channelCounts, dto.ChannelOrderCountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			OrderCount:  result.OrderCount,
		})
	}

	return channelCounts, nil
}

// GetYesterdayNewPaidOrdersCount 获取昨日新增已支付订单量（排除沙盒）
func (d *payOrderRepo) GetYesterdayNewPaidOrdersCount() (int64, error) {
	// 计算昨日的开始和结束时间
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	startTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	var count int64
	if err := d.db.Model(&model.PayOrder{}).
		Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime, endTime).
		Count(&count).Error; err != nil {
		d.log.Error("获取昨日新增已支付订单量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// GetYesterdayNewPaidOrdersByChannel 获取昨日各渠道新增已支付订单（排除沙盒）
func (d *payOrderRepo) GetYesterdayNewPaidOrdersByChannel() ([]dto.ChannelOrderCountDto, error) {
	// 计算昨日的开始和结束时间
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	startTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	type ChannelCount struct {
		ChannelID   int64  `gorm:"column:user_bind_channel_id"`
		ChannelName string `gorm:"column:user_bind_channel_name"`
		OrderCount  int64  `gorm:"column:order_count"`
	}

	var results []ChannelCount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
		Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime, endTime).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("order_count DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取昨日各渠道新增已支付订单失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelCounts []dto.ChannelOrderCountDto
	for _, result := range results {
		channelCounts = append(channelCounts, dto.ChannelOrderCountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			OrderCount:  result.OrderCount,
		})
	}

	return channelCounts, nil
}

// GetThisMonthNewOrdersCount 获取本月新增订单量
func (d *payOrderRepo) GetThisMonthNewOrdersCount() (int64, error) {
	// 计算本月的开始时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	var count int64
	if err := d.db.Model(&model.PayOrder{}).
		Where("is_delete = ? AND create_at >= ?", model.NotDeleted, startTime).
		Count(&count).Error; err != nil {
		d.log.Error("获取本月新增订单量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// GetThisMonthNewOrdersByChannel 获取本月各渠道新增订单
func (d *payOrderRepo) GetThisMonthNewOrdersByChannel() ([]dto.ChannelOrderCountDto, error) {
	// 计算本月的开始时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	type ChannelCount struct {
		ChannelID   int64  `gorm:"column:user_bind_channel_id"`
		ChannelName string `gorm:"column:user_bind_channel_name"`
		OrderCount  int64  `gorm:"column:order_count"`
	}

	var results []ChannelCount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
		Where("is_delete = ? AND create_at >= ?", model.NotDeleted, startTime).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("order_count DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取本月各渠道新增订单失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelCounts []dto.ChannelOrderCountDto
	for _, result := range results {
		channelCounts = append(channelCounts, dto.ChannelOrderCountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			OrderCount:  result.OrderCount,
		})
	}

	return channelCounts, nil
}

// GetThisMonthNewPaidOrdersCount 获取本月新增已支付订单量（排除沙盒）
func (d *payOrderRepo) GetThisMonthNewPaidOrdersCount() (int64, error) {
	// 计算本月的开始时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	var count int64
	if err := d.db.Model(&model.PayOrder{}).
		Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime).
		Count(&count).Error; err != nil {
		d.log.Error("获取本月新增已支付订单量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// GetThisMonthNewPaidOrdersByChannel 获取本月各渠道新增已支付订单（排除沙盒）
func (d *payOrderRepo) GetThisMonthNewPaidOrdersByChannel() ([]dto.ChannelOrderCountDto, error) {
	// 计算本月的开始时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	type ChannelCount struct {
		ChannelID   int64  `gorm:"column:user_bind_channel_id"`
		ChannelName string `gorm:"column:user_bind_channel_name"`
		OrderCount  int64  `gorm:"column:order_count"`
	}

	var results []ChannelCount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
		Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("order_count DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取本月各渠道新增已支付订单失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelCounts []dto.ChannelOrderCountDto
	for _, result := range results {
		channelCounts = append(channelCounts, dto.ChannelOrderCountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			OrderCount:  result.OrderCount,
		})
	}

	return channelCounts, nil
}

// GetTodayNewOrdersCount 获取本日新增订单量
func (d *payOrderRepo) GetTodayNewOrdersCount() (int64, error) {
	// 计算本日的开始和结束时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	var count int64
	if err := d.db.Model(&model.PayOrder{}).
		Where("is_delete = ? AND create_at >= ? AND create_at <= ?",
			model.NotDeleted, startTime, endTime).
		Count(&count).Error; err != nil {
		d.log.Error("获取本日新增订单量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// GetTodayNewOrdersByChannel 获取本日各渠道新增订单
func (d *payOrderRepo) GetTodayNewOrdersByChannel() ([]dto.ChannelOrderCountDto, error) {
	// 计算本日的开始和结束时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	type ChannelCount struct {
		ChannelID   int64  `gorm:"column:user_bind_channel_id"`
		ChannelName string `gorm:"column:user_bind_channel_name"`
		OrderCount  int64  `gorm:"column:order_count"`
	}

	var results []ChannelCount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
		Where("is_delete = ? AND create_at >= ? AND create_at <= ?",
			model.NotDeleted, startTime, endTime).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("order_count DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取本日各渠道新增订单失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelCounts []dto.ChannelOrderCountDto
	for _, result := range results {
		channelCounts = append(channelCounts, dto.ChannelOrderCountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			OrderCount:  result.OrderCount,
		})
	}

	return channelCounts, nil
}

// GetTodayNewPaidOrdersCount 获取本日新增已支付订单量（排除沙盒）
func (d *payOrderRepo) GetTodayNewPaidOrdersCount() (int64, error) {
	// 计算本日的开始和结束时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	var count int64
	if err := d.db.Model(&model.PayOrder{}).
		Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime, endTime).
		Count(&count).Error; err != nil {
		d.log.Error("获取本日新增已支付订单量失败: %v", err)
		return 0, err
	}

	return count, nil
}

// GetTodayNewPaidOrdersByChannel 获取本日各渠道新增已支付订单（排除沙盒）
func (d *payOrderRepo) GetTodayNewPaidOrdersByChannel() ([]dto.ChannelOrderCountDto, error) {
	// 计算本日的开始和结束时间
	now := time.Now()
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 0, 1).Add(-time.Second)

	type ChannelCount struct {
		ChannelID   int64  `gorm:"column:user_bind_channel_id"`
		ChannelName string `gorm:"column:user_bind_channel_name"`
		OrderCount  int64  `gorm:"column:order_count"`
	}

	var results []ChannelCount
	if err := d.db.Model(&model.PayOrder{}).
		Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
		Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
			model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, startTime, endTime).
		Group("user_bind_channel_id, user_bind_channel_name").
		Order("order_count DESC").
		Scan(&results).Error; err != nil {
		d.log.Error("获取本日各渠道新增已支付订单失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var channelCounts []dto.ChannelOrderCountDto
	for _, result := range results {
		channelCounts = append(channelCounts, dto.ChannelOrderCountDto{
			ChannelID:   result.ChannelID,
			ChannelName: result.ChannelName,
			OrderCount:  result.OrderCount,
		})
	}

	return channelCounts, nil
}

// GetThisMonthDailyNewPaidOrders 获取本月每日新增已支付订单统计
func (d *payOrderRepo) GetThisMonthDailyNewPaidOrders() ([]dto.DailyOrderStatsDto, error) {
	// 计算当前日期
	now := time.Now()
	currentDay := now.Day()

	var dailyStats []dto.DailyOrderStatsDto

	for day := 1; day <= currentDay; day++ {
		// 计算当天的开始和结束时间
		dayStartTime := time.Date(now.Year(), now.Month(), day, 0, 0, 0, 0, time.Local)
		dayEndTime := dayStartTime.AddDate(0, 0, 1).Add(-time.Second)

		// 获取当天总新增已支付订单
		var totalNewPaidOrders int64
		if err := d.db.Model(&model.PayOrder{}).
			Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
				model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, dayStartTime, dayEndTime).
			Count(&totalNewPaidOrders).Error; err != nil {
			d.log.Error("获取每日新增已支付订单失败: %v", err)
			return nil, err
		}

		// 获取当天各渠道新增已支付订单
		type ChannelDailyCount struct {
			ChannelID   int64  `gorm:"column:user_bind_channel_id"`
			ChannelName string `gorm:"column:user_bind_channel_name"`
			OrderCount  int64  `gorm:"column:order_count"`
		}

		var channelResults []ChannelDailyCount
		if err := d.db.Model(&model.PayOrder{}).
			Select("user_bind_channel_id, user_bind_channel_name, COUNT(*) as order_count").
			Where("order_state = ? AND pay_type != ? AND is_delete = ? AND create_at >= ? AND create_at <= ?",
				model.OrderStatePaid, model.PayTypeSandbox, model.NotDeleted, dayStartTime, dayEndTime).
			Group("user_bind_channel_id, user_bind_channel_name").
			Order("order_count DESC").
			Scan(&channelResults).Error; err != nil {
			d.log.Error("获取每日各渠道新增已支付订单失败: %v", err)
			return nil, err
		}

		// 转换为DTO
		var newPaidOrdersByChannel []dto.ChannelOrderCountDto
		for _, result := range channelResults {
			newPaidOrdersByChannel = append(newPaidOrdersByChannel, dto.ChannelOrderCountDto{
				ChannelID:   result.ChannelID,
				ChannelName: result.ChannelName,
				OrderCount:  result.OrderCount,
			})
		}

		dailyStats = append(dailyStats, dto.DailyOrderStatsDto{
			Year:                   now.Year(),
			Month:                  int(now.Month()),
			Day:                    day,
			TotalNewPaidOrders:     totalNewPaidOrders,
			NewPaidOrdersByChannel: newPaidOrdersByChannel,
		})
	}

	return dailyStats, nil
}
