package model

import "time"

type UserBindGetui struct {
	Id       int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	UserId   int64     `gorm:"column:user_id;default:0;NOT NULL" json:"user_id"` // 用户id
	ClientId string    `gorm:"column:client_id;NOT NULL" json:"client_id"`       // 个推客户端id (cid)
	CreateAt time.Time `gorm:"column:create_at;NOT NULL;autoCreateTime" json:"create_at"`
	UpdateAt time.Time `gorm:"column:update_at;NOT NULL;autoUpdateTime" json:"update_at"`
}

func (*UserBindGetui) TableName() string {
	return "user_bind_getui"
}
