## 1. 接口描述

接口请求域名： facefusion.tencentcloudapi.com 。

本接口用于单脸、多脸、选脸融合，上传人脸图片，得到与素材模板融合后的人脸图片。支持为融合结果图添加标识。查看 <a href="https://cloud.tencent.com/document/product/670/38247" target="_blank">融合接入指引</a>。

请求频率限制为20次/秒。
>
- 公共参数中的签名方式必须指定为V3版本，即配置SignatureMethod参数为TC3-HMAC-SHA256。

默认接口请求频率限制：20次/秒。

<div class="rno-api-explorer">
    <div class="rno-api-explorer-inner">
        <div class="rno-api-explorer-hd">
            <div class="rno-api-explorer-title">
                推荐使用 API Explorer
            </div>
            <a href="https://console.cloud.tencent.com/api/explorer?Product=facefusion&Version=2022-09-27&Action=FuseFacePro" class="rno-api-explorer-btn" hotrep="doc.api.explorerbtn"><i class="rno-icon-explorer"></i>点击调试</a>
        </div>
        <div class="rno-api-explorer-body">
            <div class="rno-api-explorer-cont">
                API Explorer 提供了在线调用、签名验证、SDK 代码生成和快速检索接口等能力。您可查看每次调用的请求内容和返回结果以及自动生成 SDK 调用示例。
            </div>
        </div>
    </div>
</div>

## 2. 输入参数

以下请求参数列表仅列出了接口请求参数和部分公共参数，完整公共参数列表见 [公共请求参数](/document/api/670/85622)。

| 参数名称 | 必选 | 类型 | 描述 |
|---------|---------|---------|---------|
| Action | 是 | String | [公共参数](/document/api/670/85622)，本接口取值：FuseFacePro。 |
| Version | 是 | String | [公共参数](/document/api/670/85622)，本接口取值：2022-09-27。 |
| Region | 是 | String | [公共参数](/document/api/670/85622)，详见产品支持的 [地域列表](/document/api/670/85622#.E5.9C.B0.E5.9F.9F.E5.88.97.E8.A1.A8)。 |
| RspImgType | 是 | String | 返回图像方式（url 或 base64) ，二选一。url有效期为7天。<br/>示例值：url |
| MergeInfos.N | 是 | Array of [MergeInfo](/document/api/670/85619#MergeInfo) | 用户人脸图片、素材模板图的人脸位置信息。<br/>示例值：[] |
| ModelUrl | 否 | String | 模版图片Url地址<br/>示例值：http://xxx.jpg |
| ModelImage | 否 | String | 模版图片base64<br/>示例值：base64 |
| LogoAdd | 否 | Integer | 为融合结果图添加合成标识的开关，默认为1。<br/>1：添加标识。<br/>0：不添加标识。<br/>其他数值：默认按1处理。<br/>建议您使用显著标识来提示结果图使用了人脸融合技术，是AI合成的图片。<br/>示例值：1 |
| LogoParam | 否 | [LogoParam](/document/api/670/85619#LogoParam) | 标识内容设置。<br/>默认在融合结果图右下角添加“本图片为AI合成图片”字样，您可根据自身需要替换为其他的Logo图片。<br/>示例值：{} |
| FuseParam | 否 | [FuseParam](/document/api/670/85619#FuseParam) | 融合参数。 |

## 3. 输出参数

| 参数名称 | 类型 | 描述 |
|---------|---------|---------|
| FusedImage | String | RspImgType 为 url 时，返回结果的 url， RspImgType 为 base64 时返回 base64 数据。<br/>示例值：string|
| RequestId | String | 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。|

## 4. 示例

### 示例1 图片融合

#### 输入示例

```
POST / HTTP/1.1
Host: facefusion.tencentcloudapi.com
Content-Type: application/json
X-TC-Action: FuseFacePro
<公共请求参数>

{
    "ModelUrl": "https://xxxx.png",
    "RspImgType": "url",
    "MergeInfos": [
        {
            "Url": "https://xxx.jpg"
        }
    ]
}
```

#### 输出示例

```json
{
    "Response": {
        "FusedImage": "http://xxxx.jpg",
        "RequestId": "18f26961-29d7-4d28-bd73-aa753d42e392"
    }
}
```


## 5. 开发者资源

### 腾讯云 API 平台

[腾讯云 API 平台](https://cloud.tencent.com/api) 是综合 API 文档、错误码、API Explorer 及 SDK 等资源的统一查询平台，方便您从同一入口查询及使用腾讯云提供的所有 API 服务。

### API Inspector

用户可通过 [API Inspector](https://cloud.tencent.com/document/product/1278/49361) 查看控制台每一步操作关联的 API 调用情况，并自动生成各语言版本的 API 代码，也可前往 [API Explorer](https://cloud.tencent.com/document/product/1278/46697) 进行在线调试。

### SDK

云 API 3.0 提供了配套的开发工具集（SDK），支持多种编程语言，能更方便的调用 API。

* Tencent Cloud SDK 3.0 for Python: [GitHub](https://github.com/TencentCloud/tencentcloud-sdk-python/blob/master/tencentcloud/facefusion/v20220927/facefusion_client.py) [Gitee](https://gitee.com/TencentCloud/tencentcloud-sdk-python/blob/master/tencentcloud/facefusion/v20220927/facefusion_client.py)
* Tencent Cloud SDK 3.0 for Java: [GitHub](https://github.com/TencentCloud/tencentcloud-sdk-java/blob/master/src/main/java/com/tencentcloudapi/facefusion/v20220927/FacefusionClient.java) [Gitee](https://gitee.com/TencentCloud/tencentcloud-sdk-java/blob/master/src/main/java/com/tencentcloudapi/facefusion/v20220927/FacefusionClient.java)
* Tencent Cloud SDK 3.0 for PHP: [GitHub](https://github.com/TencentCloud/tencentcloud-sdk-php/blob/master/src/TencentCloud/Facefusion/V20220927/FacefusionClient.php) [Gitee](https://gitee.com/TencentCloud/tencentcloud-sdk-php/blob/master/src/TencentCloud/Facefusion/V20220927/FacefusionClient.php)
* Tencent Cloud SDK 3.0 for Go: [GitHub](https://github.com/TencentCloud/tencentcloud-sdk-go/blob/master/tencentcloud/facefusion/v20220927/client.go) [Gitee](https://gitee.com/TencentCloud/tencentcloud-sdk-go/blob/master/tencentcloud/facefusion/v20220927/client.go)
* Tencent Cloud SDK 3.0 for Node.js: [GitHub](https://github.com/TencentCloud/tencentcloud-sdk-nodejs/blob/master/tencentcloud/services/facefusion/v20220927/facefusion_client.js) [Gitee](https://gitee.com/TencentCloud/tencentcloud-sdk-nodejs/blob/master/tencentcloud/services/facefusion/v20220927/facefusion_client.js)
* Tencent Cloud SDK 3.0 for .NET: [GitHub](https://github.com/TencentCloud/tencentcloud-sdk-dotnet/blob/master/TencentCloud/Facefusion/V20220927/FacefusionClient.cs) [Gitee](https://gitee.com/TencentCloud/tencentcloud-sdk-dotnet/blob/master/TencentCloud/Facefusion/V20220927/FacefusionClient.cs)
* Tencent Cloud SDK 3.0 for C++: [GitHub](https://github.com/TencentCloud/tencentcloud-sdk-cpp/blob/master/facefusion/src/v20220927/FacefusionClient.cpp) [Gitee](https://gitee.com/TencentCloud/tencentcloud-sdk-cpp/blob/master/facefusion/src/v20220927/FacefusionClient.cpp)
* Tencent Cloud SDK 3.0 for Ruby: [GitHub](https://github.com/TencentCloud/tencentcloud-sdk-ruby/blob/master/tencentcloud-sdk-facefusion/lib/v20220927/client.rb) [Gitee](https://gitee.com/TencentCloud/tencentcloud-sdk-ruby/blob/master/tencentcloud-sdk-facefusion/lib/v20220927/client.rb)

### 命令行工具

* [Tencent Cloud CLI 3.0](https://cloud.tencent.com/document/product/440/6176)

## 6. 错误码

以下仅列出了接口业务逻辑相关的错误码，其他错误码详见 [公共错误码](/document/api/670/85627#.E5.85.AC.E5.85.B1.E9.94.99.E8.AF.AF.E7.A0.81)。

| 错误码 | 描述 |
|---------|---------|
| FailedOperation.FaceBorderCheckFailed | 人脸配准点出框错误码。 |
| FailedOperation.FaceExceedBorder | 人脸出框，无法使用。 |
| FailedOperation.FaceFusionError | 人脸融合失败，请更换图片后重试。 |
| FailedOperation.FaceRectInvalid | 人脸框不合法。 |
| FailedOperation.FaceShapeFailed | 人脸配准失败。 |
| FailedOperation.FaceSizeTooSmall | 人脸因太小被过滤，建议人脸分辨率不小于34*34。 |
| FailedOperation.FuseFreqCtrl | 操作太频繁，触发频控。 |
| FailedOperation.FuseImageError | 图像处理出错。 |
| FailedOperation.FuseInnerError | 服务内部错误，请重试。 |
| FailedOperation.FuseMaterialNotAuth | 素材未经过审核。 |
| FailedOperation.FuseMaterialNotExist | 素材不存在。 |
| FailedOperation.FuseSavePhotoFail | 保存结果图片出错。 |
| FailedOperation.ImageDecodeFailed | 人脸检测-图片解码失败。 |
| FailedOperation.ImageDownloadError | 图片下载失败。 |
| FailedOperation.ImagePixelExceed | 素材尺寸超过1080*1080像素。 |
| FailedOperation.ImageResolutionExceed | 图片分辨率过大。建议您resize压缩到3k*3k以内。 |
| FailedOperation.ImageResolutionTooSmall | 图片短边分辨率小于64。 |
| FailedOperation.ImageSizeExceed | 输入图片base64数据大小超过5M。 |
| FailedOperation.ImageSizeInvalid | 图片尺寸过大或者过小；不满足算法要求。 |
| FailedOperation.InnerError | 服务内部错误。 |
| FailedOperation.NoFaceDetected | 无法检测出人脸, 人脸框配准分低于阈值。 |
| FailedOperation.ParameterValueError | 参数字段或者值有误。 |
| FailedOperation.ProjectNotAuth | 活动未支付授权费或已停用。 |
| FailedOperation.RequestEntityTooLarge | 请求实体太大。 |
| FailedOperation.RequestTimeout | 后端服务超时。 |
| FailedOperation.RpcFail | RPC请求失败，一般为算法微服务故障。 |
| FailedOperation.ServerError | 系统内部错误。 |
| FailedOperation.TemplateFaceIDNotExist | 素材人脸ID不存在。 |
| FailedOperation.UnKnowError | 内部错误。 |
| InvalidParameterValue.ActivityIdNotFound | 未查找到活动id。 |
| InvalidParameterValue.FaceRectParameterValueError | 人脸框参数有误或者人脸框太小。 |
| InvalidParameterValue.ImageEmpty | 人脸检测-图片为空。 |
| InvalidParameterValue.ImageIllegalDetected | 图片包含违法违规信息，审核不通过。 |
| InvalidParameterValue.MaterialIdNotFound | 未查找到素材Id。 |
| InvalidParameterValue.NoFaceInPhoto | 人脸检测-图片没有人脸。 |
| InvalidParameterValue.ParameterValueError | 参数字段或者值有误。 |
| ResourceUnavailable.Delivering | 资源正在发货中。 |
| ResourceUnavailable.Freeze | 账号已被冻结。 |
| ResourceUnavailable.GetAuthInfoError | 获取认证信息失败。 |
| ResourceUnavailable.InArrears | 账号已欠费。 |
| ResourceUnavailable.LowBalance | 余额不足。 |
| ResourceUnavailable.NotExist | 计费状态未知，请确认是否已在控制台开通服务。 |
| ResourceUnavailable.NotReady | 服务未开通。 |
| ResourceUnavailable.Recover | 资源已被回收。 |
| ResourceUnavailable.StopUsing | 账号已停服。 |
| ResourceUnavailable.UnknownStatus | 计费状态未知。 |
| ResourcesSoldOut.ChargeStatusException | 账号已欠费。 |
