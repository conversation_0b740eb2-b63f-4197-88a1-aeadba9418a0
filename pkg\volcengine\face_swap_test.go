package volcengine

import (
	"encoding/json"
	"fmt"
	"github.com/volcengine/volc-sdk-golang/service/visual"
	"github.com/volcengine/volcengine-go-sdk/service/cv20240606"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/credentials"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
	"testing"
)

func TestFaceSwapV3_3(t *testing.T) {
	visual.DefaultInstance.Client.SetAccessKey(ak)
	visual.DefaultInstance.Client.SetSecretKey(sk)

	reqBody := map[string]any{
		"req_key": "face_swap3_3",
		//"binary_data_base64": []string{"",""},
		"image_urls": []string{
			"https://musicbox-cdn.51wnl-cq.com/music-box/2025/07/16/272ac5e6a56f102f.jpg",
			"https://musicbox-cdn.51wnl-cq.com/music-box/2025/07/16/d61c47edfa904095.png",
		}, //输入换脸和模板图片链接数组，换脸图在前（最多三张），模板图在后（最多一张）
		"face_type": "l2r",
		"merge_infos": []map[string]int{
			{
				"location":          1,
				"template_location": 1,
			},
		},
		"logo_info": map[string]any{
			"add_logo":          true,
			"position":          0,
			"language":          0,
			"opacity":           0.3,
			"logo_text_content": "宠历",
		},
		"source_similarity": "0",
		"return_url":        true,
	}

	resp, status, err := visual.DefaultInstance.FaceSwapV3(reqBody)
	fmt.Println(status, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}

func TestFaceSwapV3_6(t *testing.T) {
	config := volcengine.NewConfig().
		WithRegion(region).
		WithCredentials(credentials.NewStaticCredentials(ak, sk, ""))
	sess, err := session.NewSession(config)
	if err != nil {
		t.Fatal(err)
	}
	svc := cv20240606.New(sess)

	reqMergeInfos := &cv20240606.Merge_infoForFaceSwapInput{
		Location:          volcengine.Int32(1),
		Template_location: volcengine.Int32(1),
	}
	logoInfos := &cv20240606.Logo_infoForFaceSwapInput{
		Add_logo:          volcengine.Bool(true),
		Position:          volcengine.Int32(0),
		Language:          volcengine.Int32(0),
		Logo_text_content: volcengine.String("宠历"),
	}
	faceSwapInput := &cv20240606.FaceSwapInput{
		Req_key: volcengine.String("face_swap3_6"),
		Image_urls: volcengine.StringSlice([]string{
			//"https://musicbox-cdn.51wnl-cq.com/music-box/2025/07/17/aab6926718ec49b2.jpg",
			"https://musicbox-cdn.51wnl-cq.com/music-box/2025/07/17/fb0346ee2360342c.jpg",
			//"https://musicbox-cdn.51wnl-cq.com/music-box/2025/07/17/28e9b724ceef47f6.jpg",
			"https://musicbox-cdn.51wnl-cq.com/music-box/2025/07/17/f6053bfdb3630619.png",
		}),
		Face_type:         volcengine.String("area"),
		Merge_infos:       []*cv20240606.Merge_infoForFaceSwapInput{reqMergeInfos},
		Logo_info:         logoInfos,
		Source_similarity: volcengine.String("1"),
		Return_url:        volcengine.Bool(true),
	}

	resp, err := svc.FaceSwap(faceSwapInput)
	if err != nil {
		t.Fatal(err)
	}
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}
