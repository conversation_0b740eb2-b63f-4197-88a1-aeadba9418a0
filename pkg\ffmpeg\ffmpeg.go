package ffmpeg

import (
	"fmt"
	"os/exec"
)

func IsFFmpegInstalled() bool {
	_, err := exec.LookPath("ffmpeg")
	return err == nil
}

func ConvertToMP3(inputFile, outputFile string) error {
	if !IsFFmpegInstalled() {
		return fmt.Errorf("FFmpeg is not installed on the system")
	}
	cmd := exec.Command("ffmpeg", "-i", inputFile, outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("FFmpeg error: %v, output: %s", err, string(output))
	}
	return nil
}

// ConvertToAccMP4 转换mp4 acc音频
func ConvertToAccMP4(inputFile, outputFile string) error {
	if !IsFFmpegInstalled() {
		return fmt.Errorf("FFmpeg is not installed on the system")
	}
	cmd := exec.Command("ffmpeg", "-i", inputFile, "-y", "-c:v", "copy", "-c:a", "aac", "-b:a", "128k", outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("FFmpeg error: %v, output: %s", err, string(output))
	}
	return nil
}

// MergeVideoAudio 合并视频和音频文件
func MergeVideoAudio(videoFile, audioFile, outputFile string) error {
	if !IsFFmpegInstalled() {
		return fmt.Errorf("FFmpeg is not installed on the system")
	}
	cmd := exec.Command("ffmpeg", "-i", videoFile, "-i", audioFile, "-c:v", "copy", "-c:a", "aac", "-map", "0:v:0", "-map", "1:a:0", "-shortest", "-y", outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("FFmpeg error: %v, output: %s", err, string(output))
	}
	return nil
}

// AddWatermark 给视频添加右下角水印
// videoPath: 输入视频路径
// watermarkPath: 水印图片路径（建议 PNG 透明背景）
// outputPath: 输出视频路径
// marginX, marginY: 水印距离右、下边的像素
func AddWatermark(videoPath, watermarkPath, outputPath string, marginX, marginY int) error {
	// overlay=W-w-marginX:H-h-marginY
	overlay := fmt.Sprintf("overlay=W-w-%d:H-h-%d", marginX, marginY)

	cmd := exec.Command("ffmpeg",
		"-i", videoPath,
		"-i", watermarkPath,
		"-filter_complex", overlay,
		"-codec:a", "copy", // 不重新编码音频
		outputPath,
	)
	// 获取 stdout + stderr
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("FFmpeg error: %v, output: %s", err, string(output))
	}
	return nil
}
