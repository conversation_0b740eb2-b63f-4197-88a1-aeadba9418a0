package dto

import (
	"time"
)

// JSONMap 用于处理JSON类型字段
type JSONMap map[string]any

// CreateTaskStepRequest 创建任务步骤请求
type CreateTaskStepRequest struct {
	StepName string  `json:"step_name"`
	Params   JSONMap `json:"params"`
}

// TaskDTO 任务数据传输对象
//
//	type TaskDTO struct {
//		ID          int64     `json:"id"`
//		UserID      int64     `json:"user_id"`
//		TaskType    string    `json:"task_type"`
//		Status      string    `json:"status"`
//		CurrentStep int       `json:"current_step"`
//		RetryCount  int       `json:"retry_count"`
//		ErrorMsg    string    `json:"error_msg"`
//		CreatedAt   time.Time `json:"created_at"`
//		UpdatedAt   time.Time `json:"updated_at"`
//		DeviceID    string    `json:"device_id"`
//		WorkID      uint64    `json:"work_id"`
//	}
type AiPicStepOneParams struct {
	PetPic          string  `json:"pet_pic"`
	PetDesc         string  `json:"pet_desc"`
	BackgroundDesc  string  `json:"background_desc"`
	CompositionDesc string  `json:"composition_desc"`
	Style           string  `json:"style"`
	Strength        float64 `json:"strength"`
}
type AiPicStepTwoParams struct {
	PicUrl    string `json:"pic_url"`
	StyleDesc string `json:"style_desc"`
}
type AiPicStepThreeParams struct {
	PicUrl string `json:"pic_url"`
}

// TaskStepDTO 任务步骤数据传输对象
type TaskStepDTO struct {
	ID         int64      `json:"id"`
	StepIndex  int        `json:"step_index"`
	StepName   string     `json:"step_name"`
	Status     string     `json:"status"`
	RetryCount int        `json:"retry_count"`
	Params     JSONMap    `json:"params"`
	Result     JSONMap    `json:"result"`
	ErrorMsg   string     `json:"error_msg"`
	StartedAt  *time.Time `json:"started_at"`
	FinishedAt *time.Time `json:"finished_at"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
	WorkID     uint64     `json:"work_id"`
	UserWork   *UserWorks `json:"task"`
}

// CompleteStepRequest 完成步骤请求
type CompleteStepRequest struct {
	StepID   int64   `json:"step_id"`
	Result   JSONMap `json:"result"`
	ErrorMsg string  `json:"error_msg"`
}

// GetStepWithTaskQuery 获取步骤和任务的查询条件
type GetStepQuery struct {
	Step  *TaskStepDTO `json:"step,omitempty"`  // 步骤查询条件
	Limit *int         `json:"limit,omitempty"` // 限制返回数量
}
