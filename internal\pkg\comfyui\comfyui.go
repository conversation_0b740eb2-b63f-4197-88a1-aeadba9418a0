package comfyui

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
	"time"
)

const (
	defaultTimeout = 30 * time.Second
)

// Client is a client for the ComfyUI API.
type Client struct {
	baseURL    string
	httpClient *http.Client
}

// --- Response Structs ---

// NodeError 定义节点错误的结构
type NodeError struct {
	Errors           []NodeErrorDetail `json:"errors"`
	DependentOutputs []string          `json:"dependent_outputs"`
	ClassType        string            `json:"class_type"`
}

// NodeErrorDetail 定义节点错误的详细信息
type NodeErrorDetail struct {
	Type      string                 `json:"type"`
	Message   string                 `json:"message"`
	Details   string                 `json:"details"`
	ExtraInfo map[string]interface{} `json:"extra_info"`
}

// PromptResponse defines the structure for a /prompt API call response.
type PromptResponse struct {
	PromptID   string               `json:"prompt_id"`
	Number     int                  `json:"number"`
	NodeErrors map[string]NodeError `json:"node_errors"`
}

// HistoryResponse defines the structure for a /history API call response.
type HistoryResponse map[string]HistoryPromptInfo

// HistoryPromptInfo contains details for a single prompt in the history.
type HistoryPromptInfo struct {
	Prompt  []interface{}                `json:"prompt"`
	Outputs map[string]HistoryNodeOutput `json:"outputs"`
	Status  HistoryStatusInfo            `json:"status"`
}

// HistoryNodeOutput contains the output of a node, like images.
type HistoryNodeOutput struct {
	Images []ImageInfo `json:"images"`
}

// ImageInfo contains details about a specific image.
type ImageInfo struct {
	Filename  string `json:"filename"`
	Subfolder string `json:"subfolder"`
	Type      string `json:"type"`
}

// HistoryStatusInfo contains the status of a prompt execution.
type HistoryStatusInfo struct {
	StatusStr string        `json:"status_str"`
	Completed bool          `json:"completed"`
	Messages  []interface{} `json:"messages"`
}

// QueueResponse defines the structure for a /queue API call response.
type QueueResponse struct {
	ExecInfo struct {
		QueueRemaining int `json:"queue_remaining"`
	} `json:"exec_info"`
}

// UploadImageResponse defines the structure for a /upload/image API call response.
type UploadImageResponse struct {
	Name      string `json:"name"`
	Subfolder string `json:"subfolder"`
	Type      string `json:"type"`
}

// HistoryWithStatusResponse is a custom struct for the GetHistoryWithStatus method.
type HistoryWithStatusResponse struct {
	Status   string      `json:"status"`
	Message  string      `json:"message"`
	ImageURL string      `json:"image_url,omitempty"`
	Error    interface{} `json:"error,omitempty"`
}

// NewClient creates a new ComfyUI API client.
func NewClient(baseURL string) *Client {
	return &Client{
		baseURL:    strings.TrimRight(baseURL, "/"),
		httpClient: &http.Client{Timeout: defaultTimeout},
	}
}

// History retrieves the history for a given prompt ID.
func (c *Client) History(ctx context.Context, promptID string) (*HistoryResponse, error) {
	url := fmt.Sprintf("%s/history/%s", c.baseURL, promptID)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var result HistoryResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// Prompt sends a prompt to the ComfyUI API.
func (c *Client) Prompt(ctx context.Context, clientID string, promptData map[string]interface{}) (*PromptResponse, error) {
	url := fmt.Sprintf("%s/prompt", c.baseURL)

	requestBody, err := json.Marshal(map[string]interface{}{
		"client_id": clientID,
		"prompt":    promptData,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	// 读取响应体内容
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 打印响应体字符串
	fmt.Printf("ComfyUI Prompt Response: %s\n", string(bodyBytes))

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	// 因为已经读取了响应体，需要重新创建一个Reader用于解码
	responseReader := bytes.NewReader(bodyBytes)
	var result PromptResponse
	if err := json.NewDecoder(responseReader).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w, body: %s", err, string(bodyBytes))
	}

	// 检查是否有节点错误
	if len(result.NodeErrors) > 0 {
		// 收集所有错误信息
		var errorMsgs []string
		for nodeID, nodeError := range result.NodeErrors {
			for _, err := range nodeError.Errors {
				details := err.Details
				if details == "" {
					details = err.Message
				}
				// 添加节点类型和错误类型信息
				errorMsgs = append(errorMsgs, fmt.Sprintf("节点ID %s (%s): %s [类型: %s]",
					nodeID, nodeError.ClassType, details, err.Type))
			}
		}

		// 将所有错误信息合并为一个字符串
		errorMsg := strings.Join(errorMsgs, "; ")
		return &result, fmt.Errorf("工作流执行错误: %s", errorMsg)
	}

	return &result, nil
}

// GetPromptQueue retrieves the current prompt queue.
func (c *Client) GetPromptQueue(ctx context.Context) (*QueueResponse, error) {
	url := fmt.Sprintf("%s/prompt", c.baseURL)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var result QueueResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// UploadImage uploads an image from a reader.
func (c *Client) UploadImage(ctx context.Context, reader io.Reader, filename string) (*UploadImageResponse, error) {
	url := fmt.Sprintf("%s/upload/image", c.baseURL)

	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)
	part, err := writer.CreateFormFile("image", filename)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := io.Copy(part, reader); err != nil {
		return nil, fmt.Errorf("failed to copy file content: %w", err)
	}
	writer.Close()

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, &requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var result UploadImageResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}

// GetHistoryWithStatus is a more detailed history check, similar to the Python client's logic.
func (c *Client) GetHistoryWithStatus(ctx context.Context, promptID string) (*HistoryWithStatusResponse, error) {
	historyData, err := c.History(ctx, promptID)
	if err != nil {
		return nil, err
	}

	promptHistory, ok := (*historyData)[promptID]
	if !ok {
		return &HistoryWithStatusResponse{
			Status:  "running",
			Message: "任务仍在运行中",
		}, nil
	}

	if promptHistory.Status.StatusStr == "success" {
		for _, output := range promptHistory.Outputs {
			if len(output.Images) > 0 {
				imageInfo := output.Images[0]
				imageURL := fmt.Sprintf("%s/view?filename=%s&subfolder=%s&type=%s", c.baseURL, imageInfo.Filename, imageInfo.Subfolder, imageInfo.Type)
				if imageInfo.Subfolder == "" {
					imageURL = fmt.Sprintf("%s/view?filename=%s&type=%s", c.baseURL, imageInfo.Filename, imageInfo.Type)
				}

				return &HistoryWithStatusResponse{
					Status:   "completed",
					Message:  "生成完成",
					ImageURL: imageURL,
				}, nil
			}
		}
	} else if promptHistory.Status.StatusStr == "error" {
		return &HistoryWithStatusResponse{
			Status:  "failed",
			Message: "生成失败",
			Error:   promptHistory.Status.Messages,
		}, nil
	}

	return &HistoryWithStatusResponse{
		Status:  "running",
		Message: "任务仍在运行中",
	}, nil
}

func getMimeType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".bmp":
		return "image/bmp"
	case ".webp":
		return "image/webp"
	case ".tiff", ".tif":
		return "image/tiff"
	default:
		return "image/jpeg"
	}
}
