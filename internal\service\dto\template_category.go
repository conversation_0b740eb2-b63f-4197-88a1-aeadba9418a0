package dto

import "time"

// TemplateCategoryDto 模板分类数据传输对象
type TemplateCategoryDto struct {
	ID            int64     `json:"id"`              // 主键id
	Name          string    `json:"name"`            // 分类名称
	Sort          int       `json:"sort"`            // 排序顺序，数字越小越靠前
	MaxVersion    string    `json:"max_version"`     // 最大适用版本
	MaxVersionInt int64     `json:"max_version_int"` // 最大适用版本int值
	IsActive      int8      `json:"is_active"`       // 是否启用：1-启用；-1-禁用
	IsDelete      int8      `json:"is_delete"`       // 是否删除：-1-未删除；1-已删除
	CreateAt      time.Time `json:"create_at"`       // 创建时间
	UpdateAt      time.Time `json:"update_at"`       // 更新时间
	MainClass     int       `json:"main_class"`
}

// CreateTemplateCategoryRequest 创建模板分类请求
type CreateTemplateCategoryRequest struct {
	Name       string `json:"name" binding:"required,max=100"`        // 分类名称
	Sort       int    `json:"sort"`                                   // 排序顺序，数字越小越靠前
	MaxVersion string `json:"max_version" binding:"required,max=100"` // 最大适用版本
	MainClass  int    `json:"main_class"`                             // 主分类ID
}

// UpdateTemplateCategoryRequest 更新模板分类请求
type UpdateTemplateCategoryRequest struct {
	ID         int64  `json:"id" binding:"required"`           // 主键id
	Name       string `json:"name" binding:"required,max=100"` // 分类名称
	Sort       int    `json:"sort"`                            // 排序顺序，数字越小越靠前
	MaxVersion string `json:"max_version" binding:"max=100"`   // 最大适用版本
	MainClass  int    `json:"main_class"`                      // 主分类ID
	IsActive   int8   `json:"is_active" binding:"oneof=1 -1"`  // 是否启用：1-启用；-1-禁用
	IsDelete   int8   `json:"is_delete" binding:"oneof=-1 1"`  // 是否删除：-1-未删除；1-已删除
}

// TemplateCategoryPageRequest 模板分类分页查询请求
type TemplateCategoryPageRequest struct {
	Page       int       `json:"page" form:"page" binding:"min=1"`           // 页码
	PageSize   int       `json:"page_size" form:"page_size" binding:"min=1"` // 每页大小
	Name       string    `json:"name" form:"name"`                           // 分类名称（模糊查询）
	IsActive   int8      `json:"is_active" form:"is_active"`                 // 是否启用
	IsDelete   int8      `json:"is_delete" form:"is_delete"`                 // 是否删除
	MaxVersion string    `json:"max_version" form:"max_version"`             // 最大适用版本
	MainClass  int       `json:"main_class" form:"main_class"`               // 主分类ID
	BeginAt    time.Time `json:"begin_at" form:"begin_at"`                   // 开始创建时间
	EndAt      time.Time `json:"end_at" form:"end_at"`                       // 结束创建时间
}

// TemplateCategoryPageResponse 模板分类分页查询响应
type TemplateCategoryPageResponse struct {
	Total int64                  `json:"total"` // 总数
	List  []*TemplateCategoryDto `json:"list"`  // 列表
}

// BatchDeleteTemplateCategoryRequest 批量删除模板分类请求
type BatchDeleteTemplateCategoryRequest struct {
	IDs []int64 `json:"ids" binding:"required,min=1"` // 模板分类ID列表
}

// BatchUpdateTemplateCategoryRequest 批量更新模板分类请求
type BatchUpdateTemplateCategoryRequest struct {
	IDs        []int64 `json:"ids" binding:"required,min=1"`                      // 模板分类ID列表
	IsActive   int8    `json:"is_active,omitempty"`                               // 是否启用：1-启用；-1-禁用
	IsDelete   int8    `json:"is_delete,omitempty"`                               // 是否删除：-1-未删除；1-已删除
	MaxVersion string  `json:"max_version,omitempty" binding:"omitempty,max=100"` // 最大适用版本
	MainClass  int     `json:"main_class,omitempty"`                              // 主分类ID
}

type CategoryWithTemplateResp struct {
	Category  *TemplateCategoryDto `json:"category"`
	Templates []*TemplateDto       `json:"category_templates"`
}
