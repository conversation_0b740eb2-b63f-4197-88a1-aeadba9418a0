package driver

import (
	"log"
	"os"
	"sync"
	"time"

	"chongli/component/apollo"
	logger2 "chongli/pkg/logger"

	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// sqlServerDb the singleton of SqlServer.
var sqlServerDb *gorm.DB

// initSQLServer init the singleton of SqlServer.
func initSQLServer() *gorm.DB {
	sqlServerDb = newSQLServer()
	return sqlServerDb
}

// getSQLServerDb get the singleton of SqlServer.
func getSQLServerDb() *gorm.DB {
	if sqlServerDb == nil {
		// execution once.
		var once sync.Once
		once.Do(func() {
			sqlServerDb = newSQLServer()
		})
	}
	return sqlServerDb
}

// sqlServerDbTxBegin get the singleton of SqlServer by transaction.
func sqlServerDbTxBegin() *gorm.DB {
	return getSQLServerDb().Begin()
}

// newSQLServer create a new SqlServer instance.
func newSQLServer() (sqlServerDb *gorm.DB) {
	dsn := apollo.GetApolloConfig().SQLServerDsn
	db, err := gorm.Open(sqlserver.Open(dsn), &gorm.Config{
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
			logger.Config{
				SlowThreshold: time.Second,   // 慢 SQL 阈值
				LogLevel:      logger.Silent, // Log level
				Colorful:      false,         // 禁用彩色打印
			},
		),
		SkipDefaultTransaction: true, // 禁用全局事务
	})
	if err != nil {
		logger2.Log().Panic("sqlserver连接数据库不成功:", err)
	}
	//设置连接池
	sqlDB, err := db.DB()
	if err != nil {
		logger2.Log().Panic("连接数据库不成功2", err)
	}
	sqlDB.SetMaxIdleConns(50)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Second * 30)
	sqlServerDb = db
	return
}
