package repo

import "chongli/internal/service/dto"

type UserBindGetuiRepo interface {
	BindUserIdAndCid(userId int64, cid string) error
	UpdateBind(userId int64, cid string) error
	GetBindInfoByUserId(userId int64) (*dto.UserBindGetuiInfoDto, error)
	GetBindInfoByCid(cid string) (*dto.UserBindGetuiInfoDto, error)
	DeleteBindByUserId(userId int64) error
	Page(req *dto.GetuiListRequest) ([]*dto.UserBindGetuiInfoDto, int64, error)
}
