package repo

import (
	"chongli/internal/service/dto"

	"gorm.io/gorm"
)

// GoodsVipRepo VIP商品数据仓库接口
type GoodsVipRepo interface {
	/*
		VIP商品操作
	*/

	// CreateGoods 创建商品
	CreateGoods(goods *dto.VipGoodsCreateDto, tx ...*gorm.DB) (*dto.VipGoodsInfoDto, error)
	// GetGoods 查询单条goods
	GetGoods(goodsQuery *dto.VipGoodsInfoDto, tx ...*gorm.DB) (*dto.VipGoodsInfoDto, error)
	// GetGoodsList 查询多条goods
	GetGoodsList(goodsQuery *dto.VipGoodsInfoDto, tx ...*gorm.DB) ([]*dto.VipGoodsInfoDto, error)
	// Delete 删除goods
	Delete(goods *dto.VipGoodsInfoDto, tx ...*gorm.DB) error
	// Count 获取goods数量
	Count(condition *dto.VipGoodsInfoDto, tx ...*gorm.DB) (int64, error)
	// Update 更新goods
	Update(condition, goods map[string]any, tx ...*gorm.DB) error

	/*
		渠道绑定操作
	*/

	// CreateChannel 1、创建渠道绑定
	CreateChannel(channel *dto.GoodsVipChannelCreateDto, tx ...*gorm.DB) (*dto.GoodsVipChannelDto, error)
	// GetChannelInfo 2、查询单条渠道绑定
	GetChannelInfo(where dto.GoodsVipChannelReq, tx ...*gorm.DB) (*dto.GoodsVipChannelDto, error)
	ListChannelGoods(req dto.GoodsVipChannelReq, tx ...*gorm.DB) ([]*dto.GoodsVipChannelDto, error)
	// DeleteChannelByIds 4、删除渠道绑定
	DeleteChannelByIds(id []int, tx ...*gorm.DB) error
	// CountChannelInfo 5、获取渠道绑定数量
	CountChannelInfo(where dto.GoodsVipChannelReq, tx ...*gorm.DB) (int64, error)
	// UpdateChannel 6、更新渠道绑定
	UpdateChannel(condition, goods map[string]any, tx ...*gorm.DB) error
}
