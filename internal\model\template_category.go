package model

import "time"

// TemplateCategory 模板分类
type TemplateCategory struct {
	ID            int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                                      // 主键id
	Name          string    `gorm:"column:name;NOT NULL" json:"name"`                                                    // 分类名称
	Sort          int       `gorm:"column:sort;default:0;NOT NULL" json:"sort"`                                          // 排序顺序，数字越小越靠前
	MaxVersion    string    `gorm:"column:max_version;NOT NULL" json:"max_version"`                                      // 最大适用版本
	MaxVersionInt int64     `gorm:"column:max_version_int;NOT NULL" json:"max_version_int"`                              // 最大适用版本int值
	IsActive      int8      `gorm:"column:is_active;default:1;NOT NULL" json:"is_active"`                                // 是否启用：1-启用；-1-禁用
	IsDelete      int8      `gorm:"column:is_delete;default:-1;NOT NULL" json:"is_delete"`                               // 是否删除：-1-未删除；1-已删除
	CreateAt      time.Time `gorm:"column:create_at;default:CURRENT_TIMESTAMP;NOT NULL;autoCreateTime" json:"create_at"` // 创建时间
	UpdateAt      time.Time `gorm:"column:update_at;default:CURRENT_TIMESTAMP;NOT NULL;autoUpdateTime" json:"update_at"` // 更新时间
	MainClass     int       `gorm:"column:main_class;default:0;NOT NULL" json:"main_class"`                              // 主分类
}

// TableName 表名称
func (*TemplateCategory) TableName() string {
	return "template_category"
}
