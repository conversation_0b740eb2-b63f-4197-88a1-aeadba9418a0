package model

import (
	"time"
)

// User 用户
type User struct {
	ID              int64     `json:"id" gorm:"column:id"`                              // 主键id
	DeviceId        string    `json:"device_id" gorm:"column:device_id"`                // 设备id
	Avatar          string    `json:"avatar" gorm:"column:avatar"`                      // 头像
	Username        string    `json:"username" gorm:"column:username"`                  // 用户名
	Phone           string    `json:"phone" gorm:"column:phone"`                        // 手机号
	Diamond         uint64    `json:"diamond" gorm:"column:diamond;default:0"`          // 钻石
	IsVip           int8      `json:"is_vip" gorm:"column:is_vip;default:-1"`           // 是否是vip，-1：不是；1：是
	VipType         int8      `json:"vip_type" gorm:"column:vip_type;default:-1"`       // vip类型，1：月卡；2：季卡；3：年卡
	RegisterType    int64     `json:"register_type" gorm:"column:register_type"`        // 注册方式，1：一键登录；2：验证码；3：苹果
	RegisterVersion string    `json:"register_version" gorm:"column:register_version"`  // 注册版本
	Channel         string    `json:"channel" gorm:"column:channel"`                    // 渠道
	IP              string    `json:"ip" gorm:"column:ip"`                              // IP地址
	IPLocation      string    `json:"ip_location" gorm:"column:ip_location"`            // IP地理位置
	CreateAt        time.Time `json:"create_at" gorm:"column:create_at;autoCreateTime"` // 创建时间
	UpdateAt        time.Time `json:"update_at" gorm:"column:update_at;autoUpdateTime"` // 更新时间
	IsDelete        int8      `json:"is_delete" gorm:"column:is_delete;default:-1"`     // 是否已被删除，-1：未删除；1：已注销
}

// TableName 表名称
func (*User) TableName() string {
	return "user"
}

const (
	IsVip  = 1
	NotVip = -1
)
