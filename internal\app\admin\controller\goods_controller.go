package controller

import (
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type GoodsController struct {
	goodsRepo repo.GoodsVipRepo
}
type GoodsListRequest struct {
	GoodsID       int    `json:"goods_id" form:"goods_id"`
	ChannelID     int    `json:"channel_id" form:"channel_id"`
	GoodsMiddleID string `json:"goods_middle_id" form:"goods_middle_id"`
	Page          int    `json:"page" form:"page"`
	Size          int    `json:"size" form:"size"`
}

func NewGoodsController(
	goodsRepo repo.GoodsVipRepo,
) *GoodsController {
	return &GoodsController{
		goodsRepo: goodsRepo,
	}
}
func (a *GoodsController) Add(c *gin.Context) {
	var req struct {
		ChannelIds []int `json:"channel_ids" form:"channel_ids[]" binding:"required"`
		GoodsIds   []int `json:"goods_ids" form:"goods_ids[]" binding:"required"`
	}
	if errBind := c.ShouldBind(&req); errBind != nil {
		response.Response(c, req, nil, errpkg.NewMiddleError("参数绑定失败: "+errBind.Error()), response.WithSLSLog)
		return
	}

	// 验证请求参数
	if len(req.GoodsIds) == 0 || len(req.ChannelIds) == 0 {
		response.Response(c, req, nil, errpkg.NewHighError("商品ID和渠道ID不能为空"), response.WithSLSLog)
		return
	}

	// 创建结果集合
	var results []*dto.GoodsVipChannelDto

	// 遍历所有商品和渠道组合，管理绑定关系
	for _, goodsId := range req.GoodsIds {
		// 检查商品是否存在
		goodsReq := &dto.VipGoodsInfoDto{Id: goodsId}
		goods, err := a.goodsRepo.GetGoods(goodsReq)
		if err != nil {
			response.Response(c, req, nil, errpkg.NewHighError("查询商品失败: "+err.Error()), response.WithSLSLog)
			return
		}
		if goods == nil || goods.Id == 0 || goods.IsDelete == 1 {
			response.Response(c, req, nil, errpkg.NewHighError("商品ID "+strconv.Itoa(goodsId)+" 不存在或已被删除"), response.WithSLSLog)
			return
		}

		// 获取该商品当前的所有渠道绑定
		currentBindingsReq := dto.GoodsVipChannelReq{GoodsID: int64(goodsId)}
		currentBindings, err := a.goodsRepo.ListChannelGoods(currentBindingsReq)
		if err != nil {
			response.Response(c, req, nil, errpkg.NewHighError("获取当前绑定关系失败: "+err.Error()), response.WithSLSLog)
			return
		}

		// 创建当前绑定的渠道ID映射
		currentChannelMap := make(map[int]*dto.GoodsVipChannelDto)
		for _, binding := range currentBindings {
			currentChannelMap[binding.ChannelID] = binding
		}

		// 处理新的渠道绑定
		for _, channelId := range req.ChannelIds {
			if existingBinding, exists := currentChannelMap[channelId]; exists {
				// 已存在的绑定关系，添加到结果中
				results = append(results, existingBinding)
				// 从映射中删除，标记为需要保留
				delete(currentChannelMap, channelId)
			} else {
				// 不存在的绑定关系，创建新的绑定
				channelDto := &dto.GoodsVipChannelCreateDto{
					GoodsID:   goodsId,
					ChannelID: channelId,
					Version:   goods.Version,
				}
				result, err := a.goodsRepo.CreateChannel(channelDto)
				if err != nil {
					response.Response(c, req, nil, errpkg.NewHighError("创建绑定关系失败: "+err.Error()), response.WithSLSLog)
					return
				}
				results = append(results, result)
			}
		}

		// 删除不再需要的绑定关系（currentChannelMap中剩余的都是需要删除的）
		var idsToDelete []int
		for _, binding := range currentChannelMap {
			idsToDelete = append(idsToDelete, binding.ID)
		}
		if len(idsToDelete) > 0 {
			err := a.goodsRepo.DeleteChannelByIds(idsToDelete)
			if err != nil {
				response.Response(c, req, nil, errpkg.NewHighError("删除不需要的绑定关系失败: "+err.Error()), response.WithSLSLog)
				return
			}
		}
	}

	response.Response(c, req, results, nil, response.WithSLSLog)
}

func (a *GoodsController) Delete(c *gin.Context) {
	var req struct {
		ID int `json:"id" form:"id"`
	}
	if errBind := c.BindQuery(&req); errBind != nil {
		response.Response(c, req, nil, errpkg.NewHighError("参数绑定失败: "+errBind.Error()), response.WithSLSLog)
		return
	}

	err := a.goodsRepo.DeleteChannelByIds([]int{req.ID})
	if err != nil {
		response.Response(c, req, nil, errpkg.NewHighError("删除商品VIP渠道失败: "+err.Error()), response.WithSLSLog)
		return
	}
	response.Response(c, req, nil, nil, response.WithSLSLog)
}

func (a *GoodsController) List(c *gin.Context) {
	var req GoodsListRequest
	if errBind := c.BindQuery(&req); errBind != nil {
		response.Response(c, req, nil, errpkg.NewHighError("参数绑定失败: "+errBind.Error()), response.WithSLSLog)
		return
	}
	query := dto.GoodsVipChannelReq{
		GoodsID:       int64(req.GoodsID),
		ChannelID:     req.ChannelID,
		GoodsMiddleId: req.GoodsMiddleID,
		Page:          req.Page,
		Size:          req.Size,
	}
	data, err := a.goodsRepo.ListChannelGoods(query)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewHighError("获取商品VIP渠道列表失败: "+err.Error()), response.WithSLSLog)
		return
	}
	count, err := a.goodsRepo.CountChannelInfo(query)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewHighError("获取商品VIP渠道总数失败: "+err.Error()), response.WithSLSLog)
		return
	}
	if data == nil {
		data = []*dto.GoodsVipChannelDto{}
	}
	response.Response(c, req, map[string]any{"list": data, "count": count}, nil, response.WithSLSLog)
}

// BatchDelete 批量删除商品VIP渠道绑定关系
func (a *GoodsController) BatchDelete(c *gin.Context) {
	var req struct {
		Ids []int `json:"ids" form:"ids"`
	}

	// 尝试从JSON请求体中绑定参数
	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果JSON绑定失败，尝试从查询参数中绑定
		if errQuery := c.ShouldBindQuery(&req); errQuery != nil {
			response.Response(c, req, nil, errpkg.NewHighError("参数绑定失败: "+errQuery.Error()), response.WithSLSLog)
			return
		}
	}

	// 验证请求参数
	if len(req.Ids) == 0 {
		response.Response(c, req, nil, errpkg.NewHighError("需要删除的ID不能为空"), response.WithSLSLog)
		return
	}

	// 执行批量删除操作
	err := a.goodsRepo.DeleteChannelByIds(req.Ids)
	if err != nil {
		response.Response(c, req, nil, errpkg.NewHighError("批量删除商品VIP渠道失败: "+err.Error()), response.WithSLSLog)
		return
	}

	response.Response(c, req, map[string]any{"success": true, "deleted_count": len(req.Ids)}, nil, response.WithSLSLog)
}
