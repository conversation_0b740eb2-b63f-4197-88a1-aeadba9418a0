package repo

import (
	"chongli/internal/service/dto"
	"gorm.io/gorm"
	"time"
)

// UserVipRepo 用户VIP数据仓库接口
type UserVipRepo interface {
	// GetUserVipByUserId 根据用户ID查询VIP信息
	GetUserVipByUserId(userId int64, tx ...*gorm.DB) (*dto.UserVipDto, error)
	// CreateUserVip 开通VIP
	CreateUserVip(req *dto.CreateAndRenewUserVipRequest, tx ...*gorm.DB) (*dto.UserVipDto, error)
	// RenewUserVip 续费VIP
	RenewUserVip(req *dto.CreateAndRenewUserVipRequest, tx ...*gorm.DB) error
	// UpdateUserVipIsExpireAndIsDelete 更新VIP为过期并删除
	UpdateUserVipIsExpireAndIsDelete(userId int64, tx ...*gorm.DB) error
	// CreateVipGive 创建VIP赠送记录
	CreateVipGive(req *dto.CreateVipGiveRequest, tx ...*gorm.DB) (*dto.UserVipGiveDto, error)
	// UpdateVipGiveStatus 更新赠送状态
	UpdateVipGiveStatus(id int64, isGive int8, tx ...*gorm.DB) error
	// GetVipGiveById 根据ID查询赠送记录
	GetVipGiveById(id int64, tx ...*gorm.DB) (*dto.UserVipGiveDto, error)
	// PageVipGive 分页查询VIP赠送记录
	PageVipGive(req *dto.VipGivePageRequest) (*dto.VipGivePageResponse, error)
	// GetPendingVipGivesByUserId 获取用户待赠送的记录
	GetPendingVipGivesByUserId(userId int64, tx ...*gorm.DB) ([]*dto.UserVipGiveDto, error)
	// ListTodayExpireVIP 获取今天过期的VIP
	ListTodayExpireVIP(today time.Time, tx ...*gorm.DB) ([]*dto.UserVipDto, error)
	// BatchUpdateUserVipIsExpireAndIsDelete 批量更新用户VIP过期状态
	BatchUpdateUserVipIsExpireAndIsDelete(userIds []int64, tx ...*gorm.DB) error
	// ListTodayGiveVIP 获取今天需要赠送的VIP
	ListTodayGiveVIP(today time.Time, tx ...*gorm.DB) ([]*dto.UserVipGiveDto, error)
	// ListCurrentHourGiveVIP 获取当前小时内需要赠送钻石的VIP记录（测试模式）
	ListCurrentHourGiveVIP(currentTime time.Time, tx ...*gorm.DB) ([]*dto.UserVipGiveDto, error)
	// BatchCreateVipGive 批量创建VIP赠送记录
	BatchCreateVipGive(giveList []*dto.UserVipGiveDto, tx ...*gorm.DB) error
	// GetLastVipGiveByUserId 获取用户最后一条赠送记录
	GetLastVipGiveByUserId(userId int64, tx ...*gorm.DB) (*dto.UserVipGiveDto, error)
	// BatchUpdateVipGiveStatusAndIsDelete 批量更新VIP赠送记录状态
	BatchUpdateVipGiveStatusAndIsDelete(giveList []*dto.UserVipGiveDto, tx ...*gorm.DB) error
	// GetLatestVipGivesByUserId 获取用户最新N条的VIP赠送记录
	GetLatestVipGivesByUserId(userId int64, limit int, tx ...*gorm.DB) ([]*dto.UserVipGiveDto, error)
	// BatchDeleteVipGiveById 批量删除VIP赠送记录
	BatchDeleteVipGiveById(vipGiveIds []int64, tx ...*gorm.DB) error
	// UpdateUserVipById 更新用户VIP信息
	UpdateUserVipById(vipId int64, fields map[string]any, tx ...*gorm.DB) error
}
