package response

import (
	"net/http"

	errpkg "chongli/pkg/error"
)

// httpCode response code mapping.
var httpCode = map[string]int{
	// System error code.
	InternalServerError:   http.StatusInternalServerError, // 500
	DbError:               http.StatusInternalServerError, // 500
	RedisError:            http.StatusInternalServerError, // 500
	TokenGenerateError:    http.StatusInternalServerError, // 500
	PermissionDeniedError: http.StatusUnauthorized,        // 401
	TokenError:            http.StatusUnauthorized,        // 401
	TokenExpiredError:     http.StatusUnauthorized,        // 401
	BadRequest:            http.StatusBadRequest,          // 400

	// Customer error code mapping.
	SystemBusiness:     10001,
	UserNotExist:       10002,
	LoginTypeTypeError: 10003,
	PhoneFormatError:   10004,
	HeaderError:        10005,
	TimestampError:     10006,
	SignatureError:     10007,
	UserCancelError:    10008,
}

// defaultHTTPCode default response code.
var defaultHTTPCode = map[errpkg.ErrorLevel]int{
	errpkg.ErrHighLevel:   500,
	errpkg.ErrMiddleLevel: 400,
	errpkg.ErrLowLevel:    400,
}
