package processors

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/pkg/volcengine"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/ffmpeg"
	"chongli/pkg/getui"
	"chongli/pkg/logger"
	"chongli/pkg/qiniu"
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

type DanceTaskProcessors struct {
	userService     *service.UserService
	bootstrap       *component.BootStrap
	taskService     *service.TaskService
	userWorkRepo    repo.UserWorkRepo
	log             *logger.Logger
	userTaskService *service.UserWorkService
	configService   *service.ConfigService
}

// NewDanceTaskProcessors 创建舞蹈任务处理器实例
func NewDanceTaskProcessors(
	bootstrap *component.BootStrap,
	taskService *service.TaskService,
	userWorkRepo repo.UserWorkRepo,
	userTaskService *service.UserWorkService,
	userService *service.UserService,
	configService *service.ConfigService,
) *DanceTaskProcessors {
	return &DanceTaskProcessors{
		bootstrap:       bootstrap,
		taskService:     taskService,
		userWorkRepo:    userWorkRepo,
		log:             bootstrap.Log,
		userTaskService: userTaskService,
		userService:     userService,
		configService:   configService,
	}
}

func (s *DanceTaskProcessors) PushTask() {
	ctx := context.Background()

	step, err := s.taskService.GetPendingTaskStep(ctx, model.StepNameDanceWork, nil)
	if err != nil {
		s.log.Error("获取待处理的步骤失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有待处理的步骤")
		return
	}

	// 验证参数
	params := step.Params
	petPic, ok := params["pet_pic"].(string)
	if !ok || petPic == "" {
		step.ErrorMsg = "pet_pic参数缺失或格式错误"
		s.handleStepError(ctx, step, "参数错误")
		return
	}
	videoDesc, ok := params["video_desc"].(string)
	if !ok || videoDesc == "" {
		step.ErrorMsg = "video_desc参数缺失或格式错误"
		s.handleStepError(ctx, step, "参数错误")
		return
	}

	// 创建火山引擎任务管理器
	taskManager := volcengine.NewContentGenerationTaskManager(s.bootstrap.Config.ArkApiKey)

	videoDesc = strings.TrimSpace(videoDesc) + " --watermark false"
	// 构建创建任务请求
	request := &volcengine.CreateTaskRequest{
		Model:      "doubao-seedance-1-0-lite-i2v-250428",
		TextPrompt: videoDesc,
		ImageURL:   petPic,
	}

	// 创建任务
	response, err := taskManager.CreateTask(ctx, request)
	if err != nil {
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务AI创建失败")
		return
	}

	if response.Error != "" {
		step.ErrorMsg = fmt.Sprintf("创建任务返回错误: %s", response.Error)
		s.handleStepError(ctx, step, "任务AI创建失败")
		return
	}

	// 将task_id写入result并更新状态为waiting_result
	resultJSON, err := json.Marshal(map[string]any{
		"task_id": response.ID,
	})
	if err != nil {
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "数据序列化失败")
		return
	}

	err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusWaitingResult, resultJSON)
	if err != nil {
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "状态更新失败")
		return
	}

	s.log.Info("步骤ID: %d - 舞蹈视频生成任务已提交，task_id: %s", step.ID, response.ID)
}

// QueryResult 查询舞蹈视频生成结果
func (s *DanceTaskProcessors) QueryResult() {
	ctx := context.Background()

	// 获取状态为waiting_result，步骤名为dance_work的任务
	step, err := s.getWaitingResultStep(ctx, model.StepNameDanceWork)
	if err != nil {
		s.log.Error("获取waiting_result状态的dance_work任务失败: %v", err)
		return
	}
	if step == nil {
		s.log.Info("没有waiting_result状态的dance_work任务")
		return
	}

	// 解析result字段中的task_id
	taskID, err := s.extractTaskIDFromResult(step.Result)
	if err != nil {
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务ID解析失败")
		return
	}

	// 创建火山引擎任务管理器
	taskManager := volcengine.NewContentGenerationTaskManager(s.bootstrap.Config.ArkApiKey)

	// 查询任务状态
	queryResp, err := taskManager.QueryTask(ctx, taskID)
	if err != nil {
		step.ErrorMsg = err.Error()
		s.handleStepError(ctx, step, "任务查询失败")
		return
	}

	s.log.Info("查询到的响应: %+v", queryResp)

	if queryResp.Error != "" {
		step.ErrorMsg = fmt.Sprintf("查询任务返回错误: %s", queryResp.Error)
		s.handleStepError(ctx, step, "任务查询失败")
		return
	}

	// 检查任务状态
	switch queryResp.Status {
	case volcengine.TaskStatusQueued, volcengine.TaskStatusRunning:
		s.log.Info("步骤ID: %d - 任务 %s 状态为 %s，继续等待", step.ID, taskID, queryResp.Status)
		return
	case volcengine.TaskStatusCancelled, volcengine.TaskStatusFailed:
		s.log.Error("步骤ID: %d - 任务 %s 失败，错误: %s", step.ID, taskID, queryResp.Error)
		step.ErrorMsg = fmt.Sprintf("任务失败，状态: %s", queryResp.Error)
		s.handleStepError(ctx, step, "任务执行失败")
		return
	case volcengine.TaskStatusSucceeded:
		if queryResp.VideoURL == "" {
			step.ErrorMsg = "任务成功但未获取到视频URL"
			s.handleStepError(ctx, step, "视频获取失败")
			return
		}

		// 获取音频URL参数，必须存在
		var audioURL string
		if step.Params == nil {
			step.ErrorMsg = "步骤参数为空，缺少audio_url参数"
			s.handleStepError(ctx, step, "参数缺失")
			return
		}
		audioURLParam, ok := step.Params["audio_url"].(string)
		if !ok || audioURLParam == "" {
			step.ErrorMsg = "audio_url参数缺失或格式错误"
			s.handleStepError(ctx, step, "参数错误")
			return
		}
		audioURL = audioURLParam

		// 获取用户信息判断是否VIP
		userInfo, err := s.userService.GetUserInfoByUid(int64(step.UserWork.UserID))
		if err != nil {
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "用户信息获取失败")
			return
		}

		// 下载视频并合并音频，根据VIP状态决定是否添加水印后上传到七牛
		qiniuURL, err := s.MergeAudio(queryResp.VideoURL, audioURL, userInfo.IsVip != model.IsVip)
		if err != nil {
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "视频处理失败")
			return
		}

		// 更新步骤为完成状态
		resultJSON, err := json.Marshal(map[string]any{
			"video_url": qiniuURL,
			"task_id":   taskID,
		})
		if err != nil {
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "数据序列化失败")
			return
		}

		err = s.taskService.UpdateStepStatusAndResult(ctx, step, model.StepStatusDone, resultJSON)
		if err != nil {
			step.ErrorMsg = err.Error()
			s.handleStepError(ctx, step, "状态更新失败")
			return
		}

		// 更新用户作品
		err = s.userTaskService.DoneUserWork(ctx, step.WorkID, map[string]any{
			"video_url": qiniuURL,
			"status":    model.StatusEnabled,
		})
		if err != nil {
			s.log.Error("步骤ID: %d - 获取任务失败: %v", step.ID, err)
			return
		}

		s.log.Info("步骤ID: %d - 舞蹈视频生成完成，视频URL: %s", step.ID, qiniuURL)
		_ = s.pushGetui(step, true)
	default:
		step.ErrorMsg = fmt.Sprintf("未知的任务状态: %s", queryResp.Status)
		s.handleStepError(ctx, step, "任务状态异常")
	}
}
func (s *DanceTaskProcessors) pushGetui(step *dto.TaskStepDTO, isSuccess bool) error {
	state := 1
	msgString := "宠物跳舞制作成功"
	if !isSuccess {
		state = 2
		msgString = "宠物跳舞制作失败"
	}
	payload := getui.MakePhotoPayload{
		WorkID:     step.WorkID,
		State:      state,
		PhotoCover: step.UserWork.Cover,
	}
	msg := getui.Transmission{
		Type: getui.MakePhoto,
		Msg:  msgString,
		Time: time.Now().UnixMilli(),
	}
	msg.Payload = payload
	// 发送推送
	err := s.userService.PushGetuiByUserId(step.UserWork.UserID, msg)
	if err != nil {
		return err
	}
	return nil
}

// getWaitingResultStep 获取状态为waiting_result且步骤名为指定名称的任务步骤
func (s *DanceTaskProcessors) getWaitingResultStep(ctx context.Context, stepName string) (*dto.TaskStepDTO, error) {
	limit := 1
	query := &dto.GetStepQuery{
		Step: &dto.TaskStepDTO{
			Status:   model.StepStatusWaitingResult,
			StepName: stepName,
			UserWork: &dto.UserWorks{
				Status: 0,
			},
		},
		Limit: &limit,
	}

	stepDTOs, err := s.taskService.GetStepQuery(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("获取waiting_result状态的步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return nil, nil // 没有找到对应的步骤
	}

	return stepDTOs[0], nil
}

// extractTaskIDFromResult 从result字段中解析task_id
func (s *DanceTaskProcessors) extractTaskIDFromResult(result dto.JSONMap) (string, error) {
	if result == nil {
		return "", fmt.Errorf("result字段为空")
	}

	taskID, ok := result["task_id"].(string)
	if !ok {
		return "", fmt.Errorf("result中没有找到task_id或类型错误")
	}

	if taskID == "" {
		return "", fmt.Errorf("task_id为空")
	}

	return taskID, nil
}

// handleStepError 统一处理步骤失败的错误
func (s *DanceTaskProcessors) handleStepError(ctx context.Context, step *dto.TaskStepDTO, errorMsg string) {
	// 获取调用者的文件名和行号
	_, file, line, ok := runtime.Caller(1)
	if ok {
		// 只保留文件名，不包含完整路径
		filename := filepath.Base(file)
		// 在ErrorMsg中添加调用位置信息
		if step.ErrorMsg != "" {
			step.ErrorMsg = fmt.Sprintf("%s [%s:%d]", step.ErrorMsg, filename, line)
		} else {
			step.ErrorMsg = fmt.Sprintf("错误位置: %s:%d", filename, line)
		}
	}

	step.Status = model.StepStatusFailed
	err := s.taskService.StepFail(ctx, step, errors.New(errorMsg))
	if err != nil {
		s.log.Error("步骤ID: %d - 标记步骤失败时出错: %v", step.ID, err)
	}
	//_ = s.pushGetui(step, false)
}

// downloadVideo 下载视频到本地文件
func (s *DanceTaskProcessors) downloadVideo(url, filepath string) error {
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("请求视频失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载视频失败，状态码: %d", resp.StatusCode)
	}

	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer file.Close()

	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存视频失败: %w", err)
	}

	return nil
}

// generateTempFilename 从 URL 生成临时文件名和路径
func (s *DanceTaskProcessors) generateTempFilename(url string) (string, string) {
	// 从 URL 提取文件后缀，视频默认为mp4
	ext := ".mp4" // 默认后缀

	// 使用 URL 计算 MD5 作为文件名
	hasher := md5.New()
	hasher.Write([]byte(url))
	hashBytes := hasher.Sum(nil)
	hashString := fmt.Sprintf("%x", hashBytes)

	// 创建临时文件路径
	tempDir := os.TempDir()
	tempFilePath := filepath.Join(tempDir, hashString+ext)

	return hashString + ext, tempFilePath
}

// MergeWater 给视频添加水印
func (s *DanceTaskProcessors) MergeWater(videoPath, outputPath string) error {
	ctx := context.Background()

	// 从配置服务获取水印URL
	watermarkURL, err := s.configService.GetConfigByKeyFromCache(ctx, "watermark_url")
	if err != nil {
		return fmt.Errorf("获取水印URL配置失败: %w", err)
	}
	if watermarkURL == "" {
		return fmt.Errorf("水印URL配置为空")
	}

	// 获取或下载缓存的水印文件
	watermarkPath, err := s.getOrDownloadWatermark(watermarkURL)
	if err != nil {
		return fmt.Errorf("获取水印图片失败: %w", err)
	}

	// 调用ffmpeg添加水印 (右下角，边距10px)
	err = ffmpeg.AddWatermark(videoPath, watermarkPath, outputPath, 10, 10)
	if err != nil {
		return fmt.Errorf("添加水印失败: %w", err)
	}

	s.log.Info("成功给视频添加水印")
	return nil
}

// getOrDownloadWatermark 获取或下载水印文件（带缓存）
func (s *DanceTaskProcessors) getOrDownloadWatermark(watermarkURL string) (string, error) {
	if watermarkURL == "" {
		return "", fmt.Errorf("水印URL为空")
	}

	// 生成水印文件名（基于URL的MD5）
	hasher := md5.New()
	hasher.Write([]byte(watermarkURL))
	hashBytes := hasher.Sum(nil)
	hashString := fmt.Sprintf("%x", hashBytes)

	// 水印缓存目录
	cacheDir := filepath.Join(os.TempDir(), "watermark_cache")
	err := os.MkdirAll(cacheDir, 0755)
	if err != nil {
		return "", fmt.Errorf("创建水印缓存目录失败: %w", err)
	}

	// 水印文件路径（默认png格式）
	watermarkFilePath := filepath.Join(cacheDir, hashString+".png")

	// 检查缓存是否存在
	if _, err := os.Stat(watermarkFilePath); err == nil {
		s.log.Info("使用缓存的水印文件: %s", watermarkFilePath)
		return watermarkFilePath, nil
	}

	// 下载水印
	s.log.Info("下载水印文件: %s", watermarkURL)
	err = s.downloadWatermark(watermarkURL, watermarkFilePath)
	if err != nil {
		return "", fmt.Errorf("下载水印失败: %w", err)
	}

	return watermarkFilePath, nil
}

// downloadWatermark 下载水印图片到本地文件
func (s *DanceTaskProcessors) downloadWatermark(url, filepath string) error {
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("请求水印图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载水印图片失败，状态码: %d", resp.StatusCode)
	}

	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建水印文件失败: %w", err)
	}
	defer file.Close()

	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存水印图片失败: %w", err)
	}

	return nil
}

// MergeAudio 下载视频到本地并上传到七牛云
func (s *DanceTaskProcessors) MergeAudio(videoURL, audioURL string, needWatermark bool) (string, error) {
	// 生成临时文件名和路径
	filename, tempFilePath := s.generateTempFilename(videoURL)

	// 下载视频到本地
	err := s.downloadVideo(videoURL, tempFilePath)
	if err != nil {
		return "", fmt.Errorf("下载视频失败: %w", err)
	}
	defer os.Remove(tempFilePath) // 函数执行完后删除临时文件

	// 下载或获取缓存的音频文件
	audioFilePath, err := s.getOrDownloadAudio(audioURL)
	if err != nil {
		return "", fmt.Errorf("下载音频失败: %w", err)
	}

	// 生成合并后的视频文件路径
	mergedFilename := fmt.Sprintf("merged_%s", filename)
	mergedFilePath := filepath.Join(os.TempDir(), mergedFilename)
	defer os.Remove(mergedFilePath) // 函数执行完后删除临时文件

	// 使用FFmpeg合并视频和音频
	err = ffmpeg.MergeVideoAudio(tempFilePath, audioFilePath, mergedFilePath)
	if err != nil {
		return "", fmt.Errorf("合并视频音频失败: %w", err)
	}

	s.log.Info("成功合并视频和音频")
	finalVideoPath := mergedFilePath

	// 如果需要添加水印（非VIP用户）
	if needWatermark {
		watermarkFilename := fmt.Sprintf("watermark_%s", filename)
		watermarkFilePath := filepath.Join(os.TempDir(), watermarkFilename)
		defer os.Remove(watermarkFilePath) // 函数执行完后删除临时文件

		// 添加水印
		err = s.MergeWater(finalVideoPath, watermarkFilePath)
		if err != nil {
			return "", fmt.Errorf("添加水印失败: %w", err)
		}
		finalVideoPath = watermarkFilePath
		s.log.Info("成功添加水印")
	}

	// 构建七牛云存储路径，使用 chongli-dance-videos 作为根路径
	qiniuPath := fmt.Sprintf("chongli-dance-videos/%s/%s", time.Now().Format("2006/01/02"), filename)

	// 使用 qiniu.UploadLocalFile 方法
	return qiniu.UploadLocalFile(finalVideoPath, qiniuPath)

}

// downloadAudio 下载音频到本地文件
func (s *DanceTaskProcessors) downloadAudio(url, filepath string) error {
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("请求音频失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载音频失败，状态码: %d", resp.StatusCode)
	}

	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建音频文件失败: %w", err)
	}
	defer file.Close()

	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存音频失败: %w", err)
	}

	return nil
}

// getOrDownloadAudio 获取或下载音频文件（带缓存）
func (s *DanceTaskProcessors) getOrDownloadAudio(audioURL string) (string, error) {
	if audioURL == "" {
		return "", fmt.Errorf("音频URL为空")
	}

	// 生成音频文件名（基于URL的MD5）
	hasher := md5.New()
	hasher.Write([]byte(audioURL))
	hashBytes := hasher.Sum(nil)
	hashString := fmt.Sprintf("%x", hashBytes)

	// 音频缓存目录
	cacheDir := filepath.Join(os.TempDir(), "audio_cache")
	err := os.MkdirAll(cacheDir, 0755)
	if err != nil {
		return "", fmt.Errorf("创建音频缓存目录失败: %w", err)
	}

	// 音频文件路径（默认mp3格式）
	audioFilePath := filepath.Join(cacheDir, hashString+".mp3")

	// 检查缓存是否存在
	if _, err := os.Stat(audioFilePath); err == nil {
		s.log.Info("使用缓存的音频文件: %s", audioFilePath)
		return audioFilePath, nil
	}

	// 下载音频
	s.log.Info("下载音频文件: %s", audioURL)
	err = s.downloadAudio(audioURL, audioFilePath)
	if err != nil {
		return "", fmt.Errorf("下载音频失败: %w", err)
	}

	return audioFilePath, nil
}
