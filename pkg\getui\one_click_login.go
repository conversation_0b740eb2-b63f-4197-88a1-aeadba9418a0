package getui

import (
	"chongli/component/apollo"
	"chongli/pkg/httpclient"
	"chongli/pkg/utils"
	"crypto/aes"
	"crypto/cipher"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
)

// OneClickRequest 个推一键登录请求结构体
type OneClickRequest struct {
	AppId     string `json:"appId"`
	TimeStamp int    `json:"timestamp"`
	Sign      string `json:"sign"`
	Token     string `json:"token"`
	Gyuid     string `json:"gyuid"`
}

// OneClickResponse 个推一键登录响应结构体
type OneClickResponse struct {
	Errno int `json:"errno"`
	Data  struct {
		Result string `json:"result"`
		Msg    string `json:"msg"`
		Data   struct {
			Pn string `json:"pn"`
		} `json:"data"`
	} `json:"data"`
}

// OneClickLogin 个推一键登录
func OneClickLogin(token, gyuid string) (string, error) {
	url := "https://openapi-gy.getui.com/v2/gy/ct_login/gy_get_pn"
	// 获取时间戳
	timeStamp := int(time.Now().UnixMilli())
	// 封装请求
	body, err := json.Marshal(OneClickRequest{
		AppId:     apollo.GetApolloConfig().GetuiAppId,
		TimeStamp: timeStamp,
		Token:     token,
		Gyuid:     gyuid,
		Sign:      getGeTuiSign(int64(timeStamp)),
	})
	if err != nil {
		return "", err
	}
	// 发起一键登录请求
	resp, err := httpclient.HttpPost(url, "", string(body), nil, 10*time.Second)
	if err != nil {
		return "", err
	}
	// 解析响应
	var respData OneClickResponse
	if err := json.Unmarshal([]byte(resp), &respData); err != nil {
		return "", err
	}
	// 解密电话号码
	asePhoneNumber := respData.Data.Data.Pn
	phone, decryptErr := decrypt(asePhoneNumber, apollo.GetApolloConfig().GetuiMasterSecret)
	if decryptErr != nil {
		return "", decryptErr
	}
	return phone, nil
}

// 加密获取sign
func getGeTuiSign(timeStamp int64) string {
	return utils.SHA256(apollo.GetApolloConfig().GetuiAppKey + strconv.FormatInt(timeStamp, 10) + apollo.GetApolloConfig().GetuiMasterSecret)
}

// 解密操作
func decrypt(content, key string) (string, error) {
	// 将密文从hex字符串转换为字节
	ciphertext, err := hex2Bytes(content)
	if err != nil {
		return "", err
	}
	// 获取密钥
	secretKey := getSecretKey(key)
	// 创建AES cipher
	block, err := aes.NewCipher(secretKey)
	if err != nil {
		return "", err
	}
	// 检查密文长度是否合法
	if len(ciphertext) < aes.BlockSize {
		return "", fmt.Errorf("密文太短")
	}
	// 提取IV（初始化向量）
	ivBytes := []byte(iv)
	// 创建CBC模式的解密器
	mode := cipher.NewCBCDecrypter(block, ivBytes)
	// 解密
	mode.CryptBlocks(ciphertext, ciphertext)
	// 去除PKCS5填充
	ciphertext = unpadPKCS5(ciphertext)
	return string(ciphertext), nil
}

// 获取密钥，取前16位，不足则复制
func getSecretKey(key string) []byte {
	if len(key) == 0 {
		return nil
	}
	s := key
	for len(s) < 16 {
		s += key
	}
	return []byte(s[:16])
}

// 将hex字符串转换为字节数组
func hex2Bytes(hexStr string) ([]byte, error) {
	if len(hexStr)%2 == 1 {
		hexStr = "0" + hexStr
	}
	return hex.DecodeString(hexStr)
}

// 去除PKCS5填充
func unpadPKCS5(data []byte) []byte {
	length := len(data)
	unpadding := int(data[length-1])
	return data[:(length - unpadding)]
}
