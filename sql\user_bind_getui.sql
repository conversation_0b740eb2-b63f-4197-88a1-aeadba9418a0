-- 用户绑定个推表
CREATE TABLE `user_bind_getui` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `client_id` varchar(255) NOT NULL COMMENT '个推客户端id (cid)',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户绑定个推表';

-- 索引
-- 用户ID索引，用于根据用户ID查询绑定的个推客户端
CREATE INDEX `idx_user_id` ON `user_bind_getui` (`user_id`);

-- 客户端ID索引，用于根据个推客户端ID查询绑定的用户
CREATE INDEX `idx_client_id` ON `user_bind_getui` (`client_id`);

-- 联合索引，用于用户ID和客户端ID的联合查询，避免重复绑定
CREATE UNIQUE INDEX `uk_user_client` ON `user_bind_getui` (`user_id`, `client_id`);
