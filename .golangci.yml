# This configuration file is not a recommendation.
#
# We intentionally use a limited set of linters.
# This configuration file is used with different version of golangci-lint to avoid regressions:
# the linters can change between version,
# their configuration may be not compatible or their reports can be different,
# and this can break some of our tests.
# Also, some linters are not relevant for the project (e.g. linters related to SQL).
#
# We have specific constraints, so we use a specific configuration.
#
# See the file `.golangci.reference.yml` to have a list of all available configuration options.

linters:
  disable-all: true
  # This list of linters is not a recommendation (same thing for all this configuration file).
  # We intentionally use a limited set of linters.
  # See the comment on top of this file.
  enable:
    - bodyclose
    - copyloopvar
    - depguard
    - dogsled
    - dupl
    - errcheck
    - errorlint
    - funlen
    - gocheckcompilerdirectives
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - godox
    - gofmt
    - goimports
    - mnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - intrange
    - ineffassign
    - lll
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - revive
    - staticcheck
    - stylecheck
    - testifylint
    - unconvert
    - unparam
    - unused
    - whitespace


linters-settings:
  depguard:
    rules:
      logger:
        deny:
          # logging is allowed only by logutils.Log,
          - pkg: "github.com/sirupsen/logrus"
            desc: logging is allowed only by logutils.Log.
          - pkg: "github.com/pkg/errors"
            desc: Should be replaced by standard lib errors package.
          - pkg: "github.com/instana/testify"
            desc: It's a fork of github.com/stretchr/testify.
        files:
          # logrus is allowed to use only in logutils package.
          - "!**/pkg/logutils/**.go"
  dupl:
    threshold: 100
  funlen:
    lines: -1 # the number of lines (code + empty lines) is not a right metric and leads to code without empty line or one-liner.
    statements: 50
  goconst:
    min-len: 2
    min-occurrences: 3
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport # https://github.com/go-critic/go-critic/issues/845
      - ifElseChain
      - octalLiteral
      - whyNoLint
  gocyclo:
    min-complexity: 15
  godox:
    keywords:
      - FIXME
  gofmt:
    rewrite-rules:
      - pattern: 'interface{}'
        replacement: 'any'
  goimports:
    local-prefixes: github.com/golangci/golangci-lint
  mnd:
    # don't include the "operation" and "assign"
    checks:
      - argument
      - case
      - condition
      - return
    ignored-numbers:
      - '0'
      - '1'
      - '2'
      - '3'
    ignored-functions:
      - strings.SplitN
  govet:
    settings:
      printf:
        funcs:
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf
    enable:
      - nilness
      - shadow
  errorlint:
    asserts: false
  lll:
    line-length: 140
  misspell:
    locale: US
    ignore-words:
      - "importas" # linter name
  nolintlint:
    allow-unused: false # report any unused nolint directives
    require-explanation: true # require an explanation for nolint directives
    require-specific: true # require nolint directives to be specific about which linter is being skipped
  revive:
    rules:
      - name: indent-error-flow
      - name: unexported-return
        disabled: true
      - name: unused-parameter
      - name: unused-receiver

issues:
  exclude-rules:
    - path: (.+)_test\.go
      linters:
        - dupl
        - mnd
        - lll
    - linters:
        - stylecheck
      text: "ST1003:"
    # Deprecated linter options.
    - path: pkg/golinters/errcheck/errcheck.go
      linters: [staticcheck]
      text: "SA1019: errCfg.Exclude is deprecated: use ExcludeFunctions instead"
    - path: pkg/golinters/errcheck/errcheck.go
      linters: [staticcheck]
      text: "SA1019: errCfg.Ignore is deprecated: use ExcludeFunctions instead"
    - path: pkg/golinters/govet/govet.go
      linters: [staticcheck]
      text: "SA1019: cfg.CheckShadowing is deprecated: the linter should be enabled inside Enable."
    - path: pkg/golinters/godot/godot.go
      linters: [staticcheck]
      text: "SA1019: settings.CheckAll is deprecated: use Scope instead"
    - path: pkg/goformatters/gci/gci.go
      linters: [staticcheck]
      text: "SA1019: settings.LocalPrefixes is deprecated: use Sections instead."

    # Related to `run.go`, it cannot be removed.
    - path: pkg/golinters/gofumpt/gofumpt.go
      linters: [staticcheck]
      text: "SA1019: settings.LangVersion is deprecated: use the global `run.go` instead."

    # Based on existing code, the modifications should be limited to make maintenance easier.
    - path: pkg/golinters/unused/unused.go
      linters: [gocritic]
      text: "rangeValCopy: each iteration copies 160 bytes \\(consider pointers or indexing\\)"

    # Related to the result of computation but divided multiple times by 1024.
    - path: test/bench/bench_test.go
      linters: [gosec]
      text: "G115: integer overflow conversion uint64 -> int"

  exclude-dirs:
    - test/testdata_etc # test files
    - internal/go # extracted from Go code
    - internal/x # extracted from x/tools code
    - pkg/goformatters/gci/internal # extracted from gci code
  exclude-files:
    - pkg/goanalysis/runner_checker.go # extracted from x/tools code

run:
  timeout: 5m