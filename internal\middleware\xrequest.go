package middleware

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// XRequest x-request middleware.
func XRequest() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		reqTime := fmt.Sprintf("%v", time.Now().UnixMilli())

		reqID := fmt.Sprintf("%v_%v", uuid.NewV4().String(), reqTime)

		ctx.Set("X-Request-Id", reqID)
		ctx.Set("X-Request-Time", fmt.Sprintf("%v", reqTime))

		ctx.Next()
	}
}
