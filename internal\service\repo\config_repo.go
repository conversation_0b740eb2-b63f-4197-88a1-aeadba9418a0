package repo

import (
	"chongli/internal/service/dto"
	"gorm.io/gorm"
)

// ConfigRepo 配置数据仓库接口
type ConfigRepo interface {
	// CreateConfig 创建配置
	CreateConfig(config *dto.ConfigCreateDto) (*dto.ConfigDto, error)

	// GetConfigByID 根据ID获取配置
	GetConfigByID(id int64) (*dto.ConfigDto, error)

	// GetConfigByKey 根据Key获取配置
	GetConfigByKey(key string, tx ...*gorm.DB) (*dto.ConfigDto, error)

	// GetConfigList 获取配置列表
	GetConfigList(query *dto.ConfigQueryDto) ([]*dto.ConfigDto, error)

	// GetConfigCount 获取配置总数
	GetConfigCount(query *dto.ConfigQueryDto) (int64, error)

	// UpdateConfig 更新配置
	UpdateConfig(config *dto.ConfigUpdateDto) (*dto.ConfigDto, error)

	// DeleteConfig 删除配置（软删除）
	DeleteConfig(id int64) error
}
