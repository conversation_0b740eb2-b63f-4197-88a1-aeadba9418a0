package controller

import (
	"chongli/component"
	"chongli/internal/dao"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ChannelController 营销渠道控制器
type ChannelController struct {
	log  *logger.Logger
	repo repo.MarketingChannelRepo
}

// NewChannelController 创建营销渠道控制器实例
func NewChannelController(bootStrap *component.BootStrap) *ChannelController {
	return &ChannelController{
		log:  bootStrap.Log,
		repo: dao.NewMarketingChannelRepo(bootStrap),
	}
}

// ChannelList 获取营销渠道列表
func (c *ChannelController) ChannelList(ctx *gin.Context) {
	var req dto.MarketingChannelQueryDto
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取列表
	channels, err := c.repo.GetMarketingChannelList(&req)
	if err != nil {
		c.log.Error("获取营销渠道列表失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	// 获取总数
	total, err := c.repo.GetMarketingChannelCount(&req)
	if err != nil {
		c.log.Error("获取营销渠道总数失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":      channels,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// ChannelDetail 获取营销渠道详情
func (c *ChannelController) ChannelDetail(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "无效的渠道ID",
		})
		return
	}

	// 获取渠道信息
	channel, err := c.repo.GetMarketingChannelByID(id)
	if err != nil {
		c.log.Error("获取营销渠道详情失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if channel == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    404,
			"message": "渠道不存在",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    channel,
	})
}

// ChannelAdd 创建营销渠道
func (c *ChannelController) ChannelAdd(ctx *gin.Context) {
	var req dto.MarketingChannelCreateDto
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 检查Type、BindKey、BindValue组合的唯一性
	isDuplicate, err := c.checkUniqueConstraint(req.Type, req.BindKey, req.BindValue, 0)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	if isDuplicate {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "该类型、绑定键、绑定值的组合已存在",
		})
		return
	}

	// 创建渠道
	channel, err := c.repo.CreateMarketingChannel(&req)
	if err != nil {
		c.log.Error("创建营销渠道失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    channel,
	})
}

// ChannelUpdate 更新营销渠道
func (c *ChannelController) ChannelUpdate(ctx *gin.Context) {
	var req dto.MarketingChannelUpdateDto
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 检查Type、BindKey、BindValue组合的唯一性（排除当前记录）
	isDuplicate, err := c.checkUniqueConstraint(req.Type, req.BindKey, req.BindValue, req.ID)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	if isDuplicate {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "该类型、绑定键、绑定值的组合已存在",
		})
		return
	}

	// 更新渠道
	channel, err := c.repo.UpdateMarketingChannel(&req)
	if err != nil {
		c.log.Error("更新营销渠道失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    channel,
	})
}

// ChannelDelete 删除营销渠道（软删除）
func (c *ChannelController) ChannelDelete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "无效的渠道ID",
		})
		return
	}

	// 检查渠道是否存在
	channel, err := c.repo.GetMarketingChannelByID(id)
	if err != nil {
		c.log.Error("获取营销渠道信息失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if channel == nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    404,
			"message": "渠道不存在",
		})
		return
	}

	// 执行软删除
	err = c.repo.DeleteMarketingChannel(id)
	if err != nil {
		c.log.Error("删除营销渠道失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}

// ChannelCount 获取营销渠道总数
func (c *ChannelController) ChannelCount(ctx *gin.Context) {
	var req dto.MarketingChannelQueryDto
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取总数
	total, err := c.repo.GetMarketingChannelCount(&req)
	if err != nil {
		c.log.Error("获取营销渠道总数失败: %v", err)
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"total": total,
		},
	})
}

// checkUniqueConstraint 检查Type、BindKey、BindValue组合的唯一性
// 使用现有的查询方法来检查唯一性约束
func (c *ChannelController) checkUniqueConstraint(channelType int8, bindKey, bindValue string, excludeID int) (bool, error) {
	// 构造查询条件，查找相同的Type、BindKey、BindValue组合
	queryDto := &dto.MarketingChannelQueryDto{
		Type:      &channelType,
		BindKey:   bindKey,
		BindValue: bindValue,
	}

	// 查询列表
	channels, err := c.repo.GetMarketingChannelList(queryDto)
	if err != nil {
		c.log.Error("检查唯一性约束时查询失败: %v", err)
		return false, err
	}

	// 检查结果
	for _, channel := range channels {
		// 如果是更新操作，需要排除当前记录
		if excludeID > 0 && channel.ID == excludeID {
			continue
		}
		// 如果找到了其他相同的记录，说明违反唯一性约束
		return true, nil
	}

	// 没有找到重复记录
	return false, nil
}
