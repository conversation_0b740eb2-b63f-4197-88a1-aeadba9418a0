# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup
Before building, set these environment variables:
- `APOLLO_META=http://192.168.1.150:8080/`
- `ENVIRONMENT=test`

### Build and Run
```bash
# Build the application
go build -o main .

# Start HTTP server 
./main http_start

# Start task server (cron jobs)
./main task_start

# Generate dependency injection code
make wire
```

### Prerequisites
Install Google Wire for dependency injection:
```bash
go install github.com/google/wire@latest
```

### Testing
The project uses GoConvey for testing. Run tests with:
```bash
go test ./...
```

## Architecture Overview

### Core Components

**Bootstrap System**: The `component.BootStrap` struct initializes all core services:
- Apollo configuration management
- Database drivers (MySQL, Redis, ClickHouse, PostgreSQL, SQLServer, Elasticsearch)
- Logging system
- Email service

**Server Architecture**: 
- **HTTP Server**: Gin-based REST API server on port 8080
- **Task Server**: Cron-based background task scheduler
- Both servers use the same bootstrap initialization

**Dependency Injection**: Uses Google Wire for compile-time dependency injection
- Wire configuration in `internal/server/wire.go`
- Generated code in `internal/server/wire_gen.go`
- Run `make wire` after modifying dependencies

### Directory Structure

**Application Layers**:
- `internal/app/api/` - Public API endpoints
- `internal/app/admin/` - Admin panel endpoints  
- `internal/app/task/` - Background task services
- `internal/dao/` - Data access layer
- `internal/model/` - Data models and constants
- `internal/service/` - Business logic services
- `internal/middleware/` - HTTP middleware (CORS, JWT, logging, etc.)

**Infrastructure**:
- `component/` - Core system components (Apollo, drivers, bootstrap)
- `pkg/` - Utility packages (AES, Aliyun services, email, JWT, etc.)
- `auth/` - Authentication related code

### Configuration Management

Uses Apollo for distributed configuration:
- Configuration defined in `component/apollo/apollo.go`
- Supports environment-specific configs (test/prod)
- Auto-reload on configuration changes
- Covers databases, Redis, JWT, external APIs, etc.

### Database Support

Multi-database support via driver container:
- MySQL (primary)
- Redis (caching)
- ClickHouse (analytics)
- PostgreSQL  
- SQLServer
- Elasticsearch

### Key Features

**Admin System**: Email-based authentication with verification codes
- User management with email verification
- Login logging and security controls
- JWT token-based authentication

**External Integrations**:
- Aliyun services (AFS, Green content moderation, SLS logging)
- Qiniu cloud storage
- Volcengine face swap API
- DingTalk notifications
- SMS services

**Security**: 
- JWT authentication
- CORS middleware
- Rate limiting on verification codes
- Login attempt tracking
- IP-based access control

## Development Notes

### Wire Dependency Injection
- Modify `internal/server/wire.go` for new dependencies
- Run `make wire` to regenerate injection code
- Provider sets are defined in each module's `di.go` file

### Adding New Modules
1. Create module structure in `internal/app/[module]/`
2. Add controller, service, and router layers
3. Define provider sets in `di.go`
4. Update wire configuration

### Configuration Changes
- Apollo configuration auto-reloads
- Database connection strings in Apollo
- Use environment variables for Apollo connection

### Docker Deployment
- Uses Alpine-based image
- Default command: `./main http_start`
- Configure port mappings for multiple services
- Edit `start.sh` for multi-service deployment