package service

import (
	"chongli/component"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"
	"time"

	"gorm.io/gorm"
)

const cacheKey = "chongli:config_cache:"

// ConfigService 配置服务
type ConfigService struct {
	log        *logger.Logger
	configRepo repo.ConfigRepo
	redisRepo  repo.RedisRepo
}

// NewConfigService 创建配置服务实例
func NewConfigService(bootStrap *component.BootStrap, configRepo repo.ConfigRepo, redisRepo repo.RedisRepo) *ConfigService {
	return &ConfigService{
		log:        bootStrap.Log,
		configRepo: configRepo,
		redisRepo:  redisRepo,
	}
}

// GetConfigByKeyFromCache 从缓存中获取配置
func (s *ConfigService) GetConfigByKeyFromCache(ctx context.Context, key string, tx ...*gorm.DB) (string, error) {
	// 先从 Redis 缓存获取
	cacheKey := s.buildCacheKey(key)
	data, err := s.redisRepo.Get(ctx, cacheKey)
	if err != nil {
		s.log.Error("从Redis获取配置失败, key: %s, err: %v", key, err)
		// Redis 错误不阻塞，继续从数据库获取
	}
	if data != "" {
		return data, nil
	}

	// 缓存未命中，从数据库获取
	config, err := s.configRepo.GetConfigByKey(key, tx...)
	if err != nil {
		s.log.Error("从数据库查询配置失败, key: %s, err: %v", key, err)
		return "", err
	}

	// 配置不存在
	if config == nil {
		return "", nil
	}

	// 设置到缓存
	if err := s.redisRepo.Set(ctx, cacheKey, config.ConfigValue, time.Minute*5); err != nil {
		s.log.Error("设置配置到Redis失败, key: %s, err: %v", key, err)
		// Redis 错误不阻塞，直接返回数据库结果
	}

	return config.ConfigValue, nil
}

// buildCacheKey 构建缓存键
func (s *ConfigService) buildCacheKey(key string) string {
	return cacheKey + key
}

// ClearConfigCache 清除配置缓存
func (s *ConfigService) ClearConfigCache(ctx context.Context, key string) error {
	cacheKey := s.buildCacheKey(key)
	return s.redisRepo.Del(ctx, cacheKey)
}
