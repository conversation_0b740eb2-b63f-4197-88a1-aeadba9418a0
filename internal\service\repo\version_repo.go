package repo

import (
	"chongli/internal/service/dto"

	"gorm.io/gorm"
)

// VersionRepo 版本数据仓库接口
type VersionRepo interface {
	// Create 创建版本
	Create(request *dto.VersionDto, tx ...*gorm.DB) error
	// Select 查询单个版本
	Select(request *dto.VersionQueryRequest, tx ...*gorm.DB) (*dto.VersionDto, error)
	// List 查询版本列表
	List(request *dto.VersionQueryRequest, tx ...*gorm.DB) ([]*dto.VersionDto, error)
	// PageQuery 分页查询版本
	PageQuery(request *dto.VersionPageQueryRequest, tx ...*gorm.DB) ([]*dto.VersionDto, error)
	// Count 统计版本数量
	Count(request *dto.VersionQueryRequest, tx ...*gorm.DB) (int64, error)
	// Update 更新版本（支持部分字段更新）
	Update(id int64, updates map[string]any, tx ...*gorm.DB) error
	// SelectLatestVersion 查询最新版本
	SelectLatestVersion(channel string, tx ...*gorm.DB) (*dto.VersionDto, error)
}
