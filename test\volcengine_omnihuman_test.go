package main

import (
	"encoding/json"
	"fmt"
	"github.com/volcengine/volc-sdk-golang/service/visual"
	"testing"
)

// 主体检测

func TestSubjectDetectionStep1(t *testing.T) {
	testAk := "AKLTODAzZjNjMzgxYjJlNDRiMzgzZDQ0ODU1YTdhMjlmZjU"
	testSk := "WkRBME1HSTJPVE14WkRRME5EVTBOamhsTWpCbE1UbG1ZalEzWW1JM05UQQ=="

	visual.DefaultInstance.Client.SetAccessKey(testAk)
	visual.DefaultInstance.Client.SetSecretKey(testSk)

	reqBody := map[string]any{
		"req_key":   "realman_avatar_picture_create_role_omni",
		"image_url": "https://musicbox-cdn.51wnl-cq.com/test/2.png",
	}

	// 提交任务
	resp, status, err := visual.DefaultInstance.CVSubmitTask(reqBody)
	fmt.Println(status, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}

func TestSubjectDetectionStep2(t *testing.T) {
	testAk := "AKLTODAzZjNjMzgxYjJlNDRiMzgzZDQ0ODU1YTdhMjlmZjU"
	testSk := "WkRBME1HSTJPVE14WkRRME5EVTBOamhsTWpCbE1UbG1ZalEzWW1JM05UQQ=="

	visual.DefaultInstance.Client.SetAccessKey(testAk)
	visual.DefaultInstance.Client.SetSecretKey(testSk)

	taskId := ""
	reqBody := map[string]any{
		"req_key": "realman_avatar_picture_create_role_omni",
		"task_id": taskId,
	}

	resp, status, err := visual.DefaultInstance.CVGetResult(reqBody)
	fmt.Println(status, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}

// 视频生成

func TestMakeVideoStep1(t *testing.T) {
	testAk := "AKLTODAzZjNjMzgxYjJlNDRiMzgzZDQ0ODU1YTdhMjlmZjU"
	testSk := "WkRBME1HSTJPVE14WkRRME5EVTBOamhsTWpCbE1UbG1ZalEzWW1JM05UQQ=="

	visual.DefaultInstance.Client.SetAccessKey(testAk)
	visual.DefaultInstance.Client.SetSecretKey(testSk)

	reqBody := map[string]any{
		"req_key":   "realman_avatar_picture_omni_v2",
		"image_url": "https://chongli-cdn.51wnl-cq.com/chongli/2025/08/06/902de726e4241e71.jpg",
		"audio_url": "https://chongli-cdn.51wnl-cq.com/chongli/2025/08/06/70b637bbe5ad164b.MP3",
	}

	// 提交任务
	resp, status, err := visual.DefaultInstance.CVSubmitTask(reqBody)
	fmt.Println(status, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}

func TestMakeVideoStep2(t *testing.T) {
	testAk := "AKLTODAzZjNjMzgxYjJlNDRiMzgzZDQ0ODU1YTdhMjlmZjU"
	testSk := "WkRBME1HSTJPVE14WkRRME5EVTBOamhsTWpCbE1UbG1ZalEzWW1JM05UQQ=="

	visual.DefaultInstance.Client.SetAccessKey(testAk)
	visual.DefaultInstance.Client.SetSecretKey(testSk)

	taskId := "17178390540279031933"
	reqBody := map[string]any{
		"req_key": "realman_avatar_picture_omni_v2",
		"task_id": taskId,
	}

	resp, status, err := visual.DefaultInstance.CVGetResult(reqBody)
	fmt.Println(status, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}
