package repo

import "chongli/internal/service/dto"

// MarketingChannelRepo 营销渠道数据仓库接口
type MarketingChannelRepo interface {
	// CreateMarketingChannel 创建营销渠道
	CreateMarketingChannel(channel *dto.MarketingChannelCreateDto) (*dto.MarketingChannelDto, error)

	// GetMarketingChannelByID 根据ID获取营销渠道
	GetMarketingChannelByID(id int) (*dto.MarketingChannelDto, error)

	// GetMarketingChannel 根据绑定键和绑定值获取营销渠道（单条查询，过滤已删除）
	GetMarketingChannel(query *dto.MarketingChannelQueryDto) (*dto.MarketingChannelDto, error)

	// GetMarketingChannelList 获取营销渠道列表
	GetMarketingChannelList(query *dto.MarketingChannelQueryDto) ([]*dto.MarketingChannelDto, error)

	// GetMarketingChannelCount 获取营销渠道总数
	GetMarketingChannelCount(query *dto.MarketingChannelQueryDto) (int64, error)

	// UpdateMarketingChannel 更新营销渠道
	UpdateMarketingChannel(channel *dto.MarketingChannelUpdateDto) (*dto.MarketingChannelDto, error)

	// DeleteMarketingChannel 删除营销渠道（软删除）
	DeleteMarketingChannel(id int) error
}
