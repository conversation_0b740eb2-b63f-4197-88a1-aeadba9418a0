package aliyun

import (
	"chongli/component/apollo"

	afs "github.com/alibabacloud-go/afs-20180112/client"
	rpc "github.com/alibabacloud-go/tea-rpc/client"
)

// AfsSyncScanRequestInput Afs 输入
type AfsSyncScanRequestInput struct {
	Sig       string `json:"sig"`
	SessionId string `json:"session_id"`
	Token     string `json:"token"`
	RemoteIp  string `json:"remote_ip"`
	Scene     string `json:"scene"`
}

// AfsSyncScanRequest 阿里云滑块验证
func AfsSyncScanRequest(input AfsSyncScanRequestInput) (r bool, err error) {
	apolloConfig := apollo.GetApolloConfig()
	config := new(rpc.Config)
	config.SetAccessKeyId(apolloConfig.AfsAccessKeyID).
		SetAccessKeySecret(apolloConfig.AfsAccessKeySecret).
		SetRegionId(apolloConfig.AfsRegionID).
		SetEndpoint(apolloConfig.AfsEndpoint)
	client, _ := afs.NewClient(config)
	request := new(afs.AuthenticateSigRequest)
	request.SetSig(input.Sig)                 // 签名串。必填参数，从前端获取，不可更改。
	request.SetSessionId(input.SessionId)     // 会话ID。必填参数，从前端获取，不可更改。
	request.SetToken(input.Token)             // 请求唯一表示。必填参数，从前端获取，不可更改。
	request.SetRemoteIp(input.RemoteIp)       // 客户端IP。必填参数，后端填写。
	request.SetScene(apolloConfig.AfsScene)   // 场景标识。必填参数，从前端获取，不可更改。
	request.SetAppKey(apolloConfig.AfsAppKey) // 应用类型标识。必填参数，后端填写。
	response, _err := client.AuthenticateSig(request)
	if _err != nil {
		err = _err
		return
	}

	if *response.Code == 100 {
		r = true
		return
	}
	return
}
