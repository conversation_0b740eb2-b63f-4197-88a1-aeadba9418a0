package dto

import "time"

// BannerDto Banner数据传输对象
type BannerDto struct {
	ID            int64     `json:"id"`              // 主键ID
	Title         string    `json:"title"`           // 标题
	Image         string    `json:"image"`           // 图片URL
	Video         string    `json:"video"`           // 视频URL
	Desc          string    `json:"desc"`            // 描述
	Sort          int       `json:"sort"`            // 排序，数字越小越靠前
	IsActive      int8      `json:"is_active"`       // 是否启用：1-启用；-1-禁用
	CreateAt      time.Time `json:"create_at"`       // 创建时间
	UpdateAt      time.Time `json:"update_at"`       // 更新时间
	IsDelete      int8      `json:"is_delete"`       // 是否删除：-1-未删除；1-已删除
	MaxVersion    string    `json:"max_version"`     // 最大版本号
	MaxVersionInt int64     `json:"max_version_int"` // 最大版本号整数值
	TemplateId    int64     `json:"template_id"`     // 跳转模板ID
	CategoryId    int64     `json:"category_id"`     // 跳转分类ID
	CategoryName  string    `json:"category_name"`   // 跳转分类名称
	Location      string    `json:"location"`        // 位置标识，区分不同位置的Banner
}

// CreateBannerRequest 创建Banner请求
type CreateBannerRequest struct {
	Title        string `json:"title" binding:"required,max=255"`       // 标题
	Image        string `json:"image" `                                 // 图片URL
	Video        string `json:"video" `                                 // 视频URL
	Desc         string `json:"desc" `                                  // 描述
	Sort         int    `json:"sort"`                                   // 排序，数字越小越靠前
	MaxVersion   string `json:"max_version" binding:"required,max=100"` // 最大版本号
	TemplateId   int64  `json:"template_id"`                            // 跳转模板ID
	CategoryId   int64  `json:"category_id"`                            // 跳转分类ID
	CategoryName string `json:"category_name"`                          // 跳转分类名称
	Location     string `json:"location" binding:"required"`            // 位置标识，区分不同位置的Banner
}

// UpdateBannerRequest 更新Banner请求
type UpdateBannerRequest struct {
	ID           int64  `json:"id" binding:"required"`          // 主键ID
	Title        string `json:"title" `                         // 标题
	Image        string `json:"image" `                         // 图片URL
	Video        string `json:"video" `                         // 视频URL
	Desc         string `json:"desc" `                          // 描述
	Sort         int    `json:"sort"`                           // 排序，数字越小越靠前
	IsActive     int8   `json:"is_active" binding:"oneof=1 -1"` // 是否启用：1-启用；-1-禁用
	IsDelete     int8   `json:"is_delete" binding:"oneof=-1 1"` // 是否删除：-1-未删除；1-已删除
	MaxVersion   string `json:"max_version" `                   // 最大版本号
	TemplateId   int64  `json:"template_id"`                    // 跳转模板ID
	CategoryId   int64  `json:"category_id"`                    // 跳转分类ID
	CategoryName string `json:"category_name"`                  // 跳转分类名称
	Location     string `json:"location" binding:"required"`    // 位置标识，区分不同位置的Banner
}

// BannerPageRequest Banner分页查询请求
type BannerPageRequest struct {
	Page       int       `json:"page" form:"page" binding:"min=1"`           // 页码
	PageSize   int       `json:"page_size" form:"page_size" binding:"min=1"` // 每页大小
	ID         int64     `json:"id" form:"id"`                               // 主键ID
	Title      string    `json:"title" form:"title"`                         // 标题（模糊查询）
	IsActive   int8      `json:"is_active" form:"is_active"`                 // 是否启用
	IsDelete   int8      `json:"is_delete" form:"is_delete"`                 // 是否删除
	MaxVersion string    `json:"max_version" form:"max_version"`             // 最大版本号
	BeginAt    time.Time `json:"begin_at" form:"begin_at"`                   // 开始创建时间
	EndAt      time.Time `json:"end_at" form:"end_at"`                       // 结束创建时间
	TemplateId int64     `json:"template_id" form:"template_id"`             // 跳转模板ID
	CategoryId int64     `json:"category_id" form:"category_id"`             // 跳转分类ID
	Location   string    `json:"location" form:"location"`                   // 位置标识，区分不同位置的Banner
}

// BannerPageResponse Banner分页查询响应
type BannerPageResponse struct {
	Total int64        `json:"total"` // 总数
	List  []*BannerDto `json:"list"`  // 列表
}

// BatchUpdateBannerRequest 批量更新Banner请求
type BatchUpdateBannerRequest struct {
	IDs        []int64 `json:"ids" binding:"required,min=1"`                      // Banner ID列表
	IsActive   int8    `json:"is_active,omitempty"`                               // 是否启用：1-启用；-1-禁用
	IsDelete   int8    `json:"is_delete,omitempty"`                               // 是否删除：-1-未删除；1-已删除
	MaxVersion string  `json:"max_version,omitempty" binding:"omitempty,max=100"` // 最大版本号
	Location   string  `json:"location,omitempty" binding:"omitempty,max=100"`    // 位置标识，区分不同位置的Banner
}
