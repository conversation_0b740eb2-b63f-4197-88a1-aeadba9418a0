# 构建依赖

构建之前请添加环境变量

- `APOLLO_META=http://192.168.1.150:8080/`
- `ENVIRONMENT=test`

# 启动命令

构建后, 启动命令如下
```bash
http_start # 启动 http 服务器
task_start # 启动定时任务
```
# 关于wire
本项目使用wire生成依赖注入代码, 请确保安装wire
```bash
go install github.com/google/wire@latest
```
根目录下运行
```bash
make wire
```


# launch.json文件
如果使用vscode, 请添加launch.json文件

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Package",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": ["http_start"],
            "env": {
                "APOLLO_META": "http://192.168.1.150:8080/",
                "ENVIRONMENT": "test"
            }
        }
    ]
}
```

# 公用服务使用说明

## CommonService 公共服务

CommonService 提供一些通用的服务，例如文件上传。

### 主要方法

- `PicUpload(ctx *gin.Context, req *dto.UploadMultiRequestDto) (resp []string, err errpkg.IError)` - 上传图片文件
  - 需要用户登录认证
  - 支持多文件上传
  - 文件上传到七牛云
  - 对上传的图片进行安全审核

## ConfigService 配置服务

ConfigService 通过 wire 自动注入，提供配置管理的公共服务。

### 主要方法

- `GetConfigByKeyFromCache(ctx, key, tx...)` - 从缓存获取配置值，缓存未命中时从数据库获取并设置缓存
- `ClearConfigCache(ctx, key)` - 清除指定配置的 Redis 缓存

## GuiyinService 归因服务

GuiyinService 提供设备绑定和渠道管理功能。

### 主要方法

- `GetAndUpdateChannelIDByDeviceID(deviceID, channel string) (int, error)` - 通过设备ID查询渠道ID
  - 检查设备绑定信息是否存在
  - 返回已绑定的渠道ID，不存在时返回0
  - 设备ID不能为空，否则返回错误

## PayNotifyService 支付回调服务

PayNotifyService 处理来自支付网关的支付和退款回调。

### 主要方法

- `PayNotify(req *dto.PayNotifyDto) error` - 处理支付回调通知
  - 根据通知类型自动处理支付成功或退款逻辑
  - 更新订单状态和相关信息
- `paySuccess(...)` - 处理支付成功逻辑
  - 为用户开通或续费VIP
  - 赠送购买的钻石
- `refund(...)` - 处理退款逻辑
  - 撤销用户的VIP状态
  - 扣除相应钻石

## TaskService 任务服务

TaskService 管理异步任务和其执行步骤。

### 主要方法

- `CreateSteps(ctx context.Context, req []*dto.TaskStepDTO, tx ...*gorm.DB) error` - 批量创建任务步骤
- `StepDone(ctx context.Context, step *dto.TaskStepDTO, tx ...*gorm.DB) error` - 完成一个任务步骤
- `StepFail(ctx context.Context, step *dto.TaskStepDTO, workError error, tx ...*gorm.DB) error` - 将一个任务步骤标记为失败，并更新用户作品状态
- `GetPendingTaskStep(ctx context.Context, stepType string, tx ...*gorm.DB) (*dto.TaskStepDTO, error)` - 获取一个待执行的指定类型的任务步骤
- `UpdateStepStatusAndResult(ctx context.Context, step *dto.TaskStepDTO, status string, result []byte, tx ...*gorm.DB) error` - 更新任务步骤的状态和执行结果

## TemplateService 模板服务

TemplateService 提供AI模板的管理功能。

### 主要方法

- `GetAiTemplateById(ctx context.Context, templateId uint64, tx *gorm.DB) (*dto.TemplateAIDTO, *dto.AIPicTemplate, error)` - 根据ID获取AI模板
  - 检查模板和其分类是否可用
  - 返回模板信息和解析后的参数

## UploadService 文件上传服务

UploadService 提供文件上传到七牛云存储的功能。

### 主要方法

- `Upload(req *dto.UploadRequestDto) (string, error)` - 上传文件到七牛云
  - 自动生成唯一文件名（基于MD5和时间戳）
  - 按日期组织文件路径格式：`YYYY/MM/DD/filename.ext`
  - 返回上传后的文件URL
  - 支持各种文件格式，自动转换扩展名为小写

## UserService 用户服务

UserService 提供用户信息管理和钻石余额操作功能。

### 主要方法

- `UpdateUser(req *dto.UpdateUserRequest) errpkg.IError` - 更新用户信息
  - 支持更新头像、用户名、手机号、删除状态
  - 检查用户存在性
  - 只更新非空字段

- `AddOrSubUserDiamondAndAddRecord(userId, diamond, mark, version, channel, op, tx...)` - 钻石余额操作
  - 支持增加钻石（op=1）和扣减钻石（op=-1）
  - 自动检查余额是否足够（扣减时）
  - 记录钻石变动历史
  - 支持事务操作，保证数据一致性
  - 自动更新用户余额并创建变动记录

## UserWorkService 用户作品服务

UserWorkService 管理用户的AI作品。

### 主要方法

- `CreateUserWork(ctx context.Context, work *dto.UserWorks, tx ...*gorm.DB) (*dto.UserWorks, error)` - 创建用户作品记录
- `GetUserWorkList(ctx context.Context, query *dto.GetUserWorkListRequest, tx ...*gorm.DB) ([]*dto.UserWorks, int64, error)` - 获取用户作品列表
- `UpdateUserWorkOnFailure(ctx context.Context, workID uint64, errorMsg string, tx ...*gorm.DB) error` - 在任务失败时更新作品状态
  - 将作品状态设置为失败
  - 返还用户因此次生成所消耗的钻石
- `DeleteUserWork(ctx context.Context, id uint64, tx ...*gorm.DB) error` - 删除用户作品

## Utils 工具函数

### 版本处理函数

- `utils.VersionToVersionInt(versionStr string) int64` - 将版本字符串转换为可比较的整数
  - 使用 `hashicorp/go-version` 库，支持语义化版本控制
  - 例如：`"1.2.3"` → `1002003`，`"2.10.15"` → `2010015`
  - 支持各种版本格式，包括带 `v` 前缀的版本号
  - 解析失败时返回 `0`