package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"fmt"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type configRepo struct {
	log *logger.Logger
	db  *gorm.DB
	rds *redis.Client
}

func NewConfigRepo(bootStrap *component.BootStrap) repo.ConfigRepo {
	return &configRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

func (d *configRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 {
		return tx[0]
	}
	return d.db
}

// GetConfigByKey 根据Key获取配置
func (d *configRepo) GetConfigByKey(key string, tx ...*gorm.DB) (*dto.ConfigDto, error) {
	var config model.Config
	err := d.getDb(tx).Where("config_key = ? AND is_delete = ?", key, model.StatusDisabled).First(&config).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据Key查询配置失败: %v", err)
		return nil, err
	}

	return d.convertToDto(&config), nil
}

// CreateConfig 创建配置
func (d *configRepo) CreateConfig(config *dto.ConfigCreateDto) (*dto.ConfigDto, error) {
	modelConfig := &model.Config{
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		Remark:      config.Remark,
		IsChannel:   config.IsChannel,
		IsDelete:    config.IsDelete,
		IsPublish:   config.IsPublish,
	}

	err := d.db.Create(modelConfig).Error
	if err != nil {
		d.log.Error("创建配置失败: %v", err)
		return nil, err
	}

	return d.convertToDto(modelConfig), nil
}

// GetConfigByID 根据ID获取配置
func (d *configRepo) GetConfigByID(id int64) (*dto.ConfigDto, error) {
	var config model.Config
	err := d.db.Where("id = ? ", id).First(&config).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据ID查询配置失败: %v", err)
		return nil, err
	}

	return d.convertToDto(&config), nil
}

// GetConfigList 获取配置列表
func (d *configRepo) GetConfigList(query *dto.ConfigQueryDto) ([]*dto.ConfigDto, error) {
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 10
	}

	queryBuilder := d.db.Model(&model.Config{})
	queryBuilder = d.buildQueryCondition(queryBuilder, query)

	var configs []*model.Config
	offset := (query.Page - 1) * query.PageSize
	err := queryBuilder.Offset(offset).Limit(query.PageSize).Order("id DESC").Find(&configs).Error
	if err != nil {
		d.log.Error("查询配置列表失败: %v", err)
		return nil, err
	}

	var configDtos []*dto.ConfigDto
	for _, config := range configs {
		configDtos = append(configDtos, d.convertToDto(config))
	}

	return configDtos, nil
}

// GetConfigCount 获取配置总数
func (d *configRepo) GetConfigCount(query *dto.ConfigQueryDto) (int64, error) {
	queryBuilder := d.db.Model(&model.Config{})
	queryBuilder = d.buildQueryCondition(queryBuilder, query)

	var total int64
	err := queryBuilder.Count(&total).Error
	if err != nil {
		d.log.Error("查询配置总数失败: %v", err)
		return 0, err
	}

	return total, nil
}

// UpdateConfig 更新配置
func (d *configRepo) UpdateConfig(config *dto.ConfigUpdateDto) (*dto.ConfigDto, error) {
	existingConfig, err := d.GetConfigByID(config.Id)
	if err != nil {
		return nil, err
	}
	if existingConfig == nil {
		return nil, fmt.Errorf("配置不存在，ID: %d", config.Id)
	}

	updates := map[string]any{
		"config_key":   config.ConfigKey,
		"config_value": config.ConfigValue,
		"remark":       config.Remark,
		"is_channel":   config.IsChannel,
		"is_delete":    config.IsDelete,
		"is_publish":   config.IsPublish,
	}

	err = d.db.Model(&model.Config{}).Where("id = ?", config.Id).Updates(updates).Error
	if err != nil {
		d.log.Error("更新配置失败: %v", err)
		return nil, err
	}

	return d.GetConfigByID(config.Id)
}

// DeleteConfig 删除配置（软删除）
func (d *configRepo) DeleteConfig(id int64) error {
	err := d.db.Model(&model.Config{}).Where("id = ?", id).Update("is_delete", 1).Error
	if err != nil {
		d.log.Error("删除配置失败: %v", err)
		return err
	}
	return nil
}

// buildQueryCondition 构建查询条件
func (d *configRepo) buildQueryCondition(query *gorm.DB, queryDto *dto.ConfigQueryDto) *gorm.DB {
	if queryDto.Id > 0 {
		query = query.Where("id = ?", queryDto.Id)
	}

	if queryDto.ConfigKey != "" {
		query = query.Where("config_key LIKE ?", "%"+queryDto.ConfigKey+"%")
	}

	if queryDto.IsDelete != 0 {
		query = query.Where("is_delete = ?", queryDto.IsDelete)
	}

	if queryDto.IsChannel != 0 {
		query = query.Where("is_channel = ?", queryDto.IsChannel)
	}

	if queryDto.IsPublish != 0 {
		query = query.Where("is_publish = ?", queryDto.IsPublish)
	}

	return query
}

// convertToDto 将model转换为dto
func (d *configRepo) convertToDto(config *model.Config) *dto.ConfigDto {
	if config == nil {
		return nil
	}

	return &dto.ConfigDto{
		Id:          config.Id,
		ConfigKey:   config.ConfigKey,
		ConfigValue: config.ConfigValue,
		Remark:      config.Remark,
		CreateAt:    config.CreateAt,
		IsDelete:    model.StatusFlag(config.IsDelete),
		IsChannel:   model.StatusFlag(config.IsChannel),
		IsPublish:   model.StatusFlag(config.IsPublish),
	}
}
