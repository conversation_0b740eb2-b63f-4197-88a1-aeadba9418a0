package getui

import (
	"chongli/pkg/httpclient"
	"chongli/pkg/logger"
	"encoding/json"
	"strconv"
	"time"
)

// func TestPushMsgByCid(t *testing.T) {
// 	requestId := utils.CreateUUid()
// 	cid := "a9b719c79e61a86ed3ae563ae7892cce"
// 	msg := &Transmission{
// 		Type: MakePhoto,
// 		Payload: MakePhotoPayload{
// 			PhotoId:    1,
// 			PhotoCover: "test photo cover",
// 			State:      1,
// 			ErrMsg:     "test err msg",
// 			CreateAt:   time.Now().UnixMilli(),
// 		},
// 		Msg:  "写真制作成功",
// 		Time: time.Now().UnixMilli(),
// 	}
// 	ttl := 3000
// 	respData, err := PushMsgByCid(requestId, cid, msg, ttl)
// 	if err != nil {
// 		t.Error(err)
// 	} else {
// 		t.Log(respData)
// 	}
// }

// func TestPushMsgByCid2(t *testing.T) {
// 	const (
// 		getuiAppId        = "5LlhX00A6S67mWBIvx2MG7"
// 		getuiAppKey       = "WThTaQtlYo7gL2ZtvXVJy9"
// 		getuiMasterSecret = "dHhbRe28wc62BJOzEi3oO6"
// 	)
// 	requestId := utils.CreateUUid()
// 	cid := "c3c56ecc3a9fbaee9fc15f02f0bdf182"
// 	ttl := 3000

// 	var msgList = []*Transmission{
// 		{
// 			Type: MakePhoto,
// 			Payload: MakePhotoPayload{
// 				PhotoId:    1,
// 				PhotoCover: "http://chongli-cdn.51wnl-cq.com/chongli/2025/08/06/48cce96c27975a536611caff65dc72a3.png",
// 				PhotoTitle: "test photo title",
// 				State:      1,
// 				ErrMsg:     "test err msg",
// 				CreateAt:   time.Now().UnixMilli(),
// 			},
// 			Msg:  "success",
// 			Time: time.Now().UnixMilli(),
// 		},
// 		{
// 			Type: MakeSinging,
// 			Payload: MakeSingingPayload{
// 				SingingID:    1,
// 				SingingCover: "http://chongli-cdn.51wnl-cq.com/chongli/2025/08/06/48cce96c27975a536611caff65dc72a3.png",
// 				SingingTitle: "test singing title",
// 				State:        1,
// 				ErrMsg:       "test err msg",
// 				CreateAt:     time.Now().UnixMilli(),
// 			},
// 			Msg:  "success",
// 			Time: time.Now().UnixMilli(),
// 		},
// 		{
// 			Type: MakeDancing,
// 			Payload: MakeDancingPayload{
// 				DancingID:    1,
// 				DancingCover: "http://chongli-cdn.51wnl-cq.com/chongli/2025/08/06/48cce96c27975a536611caff65dc72a3.png",
// 				DancingTitle: "test dancing title",
// 				State:        1,
// 				ErrMsg:       "test err msg",
// 				CreateAt:     time.Now().UnixMilli(),
// 			},
// 			Msg:  "success",
// 			Time: time.Now().UnixMilli(),
// 		},
// 		{
// 			Type: MakePhoto,
// 			Payload: MakePhotoPayload{
// 				PhotoId:    1,
// 				PhotoCover: "",
// 				State:      2,
// 				ErrMsg:     "test err msg",
// 				CreateAt:   time.Now().UnixMilli(),
// 			},
// 			Msg:  "fail",
// 			Time: time.Now().UnixMilli(),
// 		},
// 		{
// 			Type: MakeSinging,
// 			Payload: MakeSingingPayload{
// 				SingingID:    1,
// 				SingingCover: "",
// 				State:        2,
// 				ErrMsg:       "test err msg",
// 				CreateAt:     time.Now().UnixMilli(),
// 			},
// 			Msg:  "fail",
// 			Time: time.Now().UnixMilli(),
// 		},
// 		{
// 			Type: MakeDancing,
// 			Payload: MakeDancingPayload{
// 				DancingID:    1,
// 				DancingCover: "",
// 				State:        2,
// 				ErrMsg:       "test err msg",
// 				CreateAt:     time.Now().UnixMilli(),
// 			},
// 			Msg:  "fail",
// 			Time: time.Now().UnixMilli(),
// 		},
// 	}

// 	respData, err := pushMsgByCidFunc(requestId, cid, msgList[2], ttl, getuiAppId, getuiAppKey, getuiMasterSecret)
// 	if err != nil {
// 		t.Error(err)
// 	} else {
// 		t.Log(respData)
// 	}
// }

func pushMsgByCidFunc(requestId, cid string, tss *Transmission, ttl int, getuiAppid, getuiAppkey, getuiMasterSecret string) (any, error) {
	// 打印日志
	logger.Log().Info("Push message by cid: %s, requestId: %s, message type: %s", cid, requestId, tss.Type)

	url := baseUrl + getuiAppid + "/push/single/cid"
	token, expireTime, _ = authFunc(getuiAppid, getuiAppkey, getuiMasterSecret)
	jsonMsg, err := json.Marshal(tss)
	if err != nil {
		return "", err
	}
	reqBody, err := json.Marshal(PushMsgRequest{
		RequestId: requestId,
		Settings:  settings{TTL: ttl},
		Audience:  audience{Cid: []string{cid}},
		PushMessage: pushMessage{
			Transmission: string(jsonMsg),
		},
	})
	if err != nil {
		return "", err
	}
	resp, err := httpclient.HttpPost(url, "", string(reqBody), map[string]string{"token": token, "Content-Type": "application/json;charset=utf-8"}, 10*time.Second)
	if err != nil {
		return "", err
	}
	var respData PushMsgResponse
	if err := json.Unmarshal([]byte(resp), &respData); err != nil {
		return "", err
	}
	if respData.Code == 10001 {
		token, expireTime, _ = auth()
	}
	return respData.Data, nil
}

func authFunc(getuiAppId, getuiAppKey, getuiMasterSecret string) (string, string, error) {
	url := baseUrl + getuiAppId + "/auth"
	timeStamp := time.Now().UnixMilli()
	sign := getSign(getuiAppKey, strconv.FormatInt(timeStamp, 10), getuiMasterSecret)
	body, err := json.Marshal(AuthRequest{
		Sign:      sign,
		TimeStamp: strconv.FormatInt(timeStamp, 10),
		AppKey:    getuiAppKey,
	})
	if err != nil {
		return "", "", err
	}
	resp, err := httpclient.HttpPost(url, "", string(body), map[string]string{"Content-Type": "application/json;charset=utf-8"}, 10*time.Second)
	if err != nil {
		return "", "", err
	}
	var respData AuthResponse
	if err := json.Unmarshal([]byte(resp), &respData); err != nil {
		return "", "", err
	}
	return respData.Data.Token, respData.Data.ExpireTime, nil
}
