package driver

import (
	"chongli/component/apollo"
	"chongli/pkg/logger"
	"fmt"
	"sync"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"golang.org/x/net/context"
)

// IClickHouseDriver the methods of ClickHouseDriver provided.
type IClickHouseDriver interface {
	GetConn() driver.Conn
	GetContext() context.Context
}

// ClickHouseDriver the ClickHouse driver, it include Conn and Ctx.
type ClickHouseDriver struct {
	conn driver.Conn
	ctx  context.Context
}

// clickHouseDriver singleton of clickHouseDriver.
var clickHouseDriver *ClickHouseDriver

// initClickHouse init singleton of clickHouseDriver.
func initClickHouse() (driver *ClickHouseDriver) {
	return newClickHouse()
}

// getClickHouseDriver get singleton of clickHouseDriver.
func getClickHouseDriver() IClickHouseDriver {
	if clickHouseDriver == nil {
		// execution once.
		var once sync.Once
		once.Do(func() {
			clickHouseDriver = newClickHouse()
		})
	}
	return clickHouseDriver
}

// GetConn get the ClickHouse connection.
func (d *ClickHouseDriver) GetConn() driver.Conn {
	return d.conn
}

// GetContext get the ClickHouse context.
func (d *ClickHouseDriver) GetContext() context.Context {
	return d.ctx
}

// newClickHouse create a instance of type ClickHouseDriver.
func newClickHouse() (driver *ClickHouseDriver) {
	apolloConfig := apollo.GetApolloConfig()
	var err error
	// 外网地址
	driver = &ClickHouseDriver{}
	driver.conn, err = clickhouse.Open(&clickhouse.Options{
		Addr: []string{apolloConfig.ClickHouseAddr},
		Auth: clickhouse.Auth{
			Database: apolloConfig.ClickHouseDB,
			Username: apolloConfig.ClickHouseUser,
			Password: apolloConfig.ClickHousePass,
		},
		Settings: clickhouse.Settings{
			"max_execution_time": 60,
		},

		DialTimeout:     5 * time.Second,
		ReadTimeout:     5 * time.Second,
		ConnMaxLifetime: 1 * time.Hour,
		Compression: &clickhouse.Compression{
			Method: clickhouse.CompressionLZ4,
		},
		MaxIdleConns: 20,
		MaxOpenConns: 25,
		//Debug:        true,
	})
	if err != nil {
		logger.Log().Panic(fmt.Sprintf("clickhouse链接池创建异常:%v\r\n", err.Error()))
	}
	driver.ctx = clickhouse.Context(context.Background(), clickhouse.WithSettings(clickhouse.Settings{
		"max_block_size": 655, // 此值待优化
	}))
	err = driver.conn.Ping(driver.ctx)
	if err != nil {
		logger.Log().Panic(fmt.Sprintf("clickhouse ping 异常:%v\r\n", err.Error()))
	}
	logger.Log().Println("clickhouse init...")
	return
}
