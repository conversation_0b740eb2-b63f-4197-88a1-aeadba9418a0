package response

// 系统错误
const (
	InternalServerError   = "server internal error"
	DbError               = "database error"
	RedisError            = "redis error"
	BadRequest            = "bad request"
	PermissionDeniedError = "permission denied"
	TokenExpiredError     = "token expired"
	TokenError            = "token error"
	TokenGenerateError    = "token generate error"
)

// 业务错误
const (
	SystemBusiness     = "system business"
	UserNotExist       = "user not exist"
	UserIdError        = "user id error"
	LoginTypeTypeError = "登录请求类型错误"
	PhoneFormatError   = "手机号码无效"
	HeaderError        = "请求头信息缺失"
	TimestampError     = "时间戳失效"
	SignatureError     = "签名错误"
	SendCodeError      = "发送验证码失败"
	GeTuiServiceError  = "个推服务异常"
	UserCancelError    = "该账户已申请注销，如有疑问请联系客服"
	VersionNotExit     = "版本信息不存在"
	ChannelNotExit     = "客户端渠道信息缺失"
	ConfigNotExit      = "配置信息不存在"
	UploadFileError    = "上传文件失败"
)
