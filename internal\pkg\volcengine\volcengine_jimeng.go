package volcengine

import (
	"encoding/json"
	"fmt"

	"github.com/volcengine/volc-sdk-golang/service/visual"
)

// Config 火山引擎配置
type Config struct {
	AccessKey string
	SecretKey string
	Region    string
	Host      string
}

// Client 火山引擎客户端
type Client struct {
	config *Config
}

// NewClient 创建新的火山引擎客户端
func NewClient(config *Config) *Client {
	client := &Client{
		config: config,
	}

	// 设置访问密钥
	visual.DefaultInstance.Client.SetAccessKey(config.AccessKey)
	visual.DefaultInstance.Client.SetSecretKey(config.SecretKey)

	// 可选设置区域和主机
	if config.Region != "" {
		visual.DefaultInstance.SetRegion(config.Region)
	}
	if config.Host != "" {
		visual.DefaultInstance.SetHost(config.Host)
	}

	return client
}

// 常量定义
const (
	// 火山图生图
	SeededitReqKey = "seededit_v3.0"
	// 火山角色生成，大图灵动模式
	RealmanAvatarPictureCreateRoleLoopy = "realman_avatar_picture_create_role_loopy"
	// 火山对口型生成，大图灵动模式
	RealmanAvatarPictureLoopy = "realman_avatar_picture_loopy"
)

// SubmitTaskRequest 提交任务请求参数（通用）
type SubmitTaskRequest struct {
	ReqBody map[string]interface{} `json:"req_body"`
}

// SubmitTaskResponse 提交任务响应（通用）
type SubmitTaskResponse struct {
	Status      int    `json:"status"`
	Code        int    `json:"code"`
	Message     string `json:"message"`
	RequestID   string `json:"request_id"`
	TimeElapsed string `json:"time_elapsed"`
	Data        struct {
		TaskID string `json:"task_id"`
	} `json:"data"`
	Error string `json:"error,omitempty"`
}

// GetResultRequest 获取任务结果请求参数（通用）
type GetResultRequest struct {
	ReqKey string `json:"req_key"`
	TaskID string `json:"task_id"`
}

// GetResultResponse 获取任务结果响应（通用）
type GetResultResponse struct {
	Status      int    `json:"status"`
	Code        int    `json:"code"`
	Message     string `json:"message"`
	RequestID   string `json:"request_id"`
	TimeElapsed string `json:"time_elapsed"`
	Data        struct {
		BinaryDataBase64 []string `json:"binary_data_base64"`
		ImageUrls        []string `json:"image_urls"`
		RespData         string   `json:"resp_data"`
		Status           string   `json:"status"`
	} `json:"data"`
	Error string `json:"error,omitempty"`
}

// 类型别名，保持向后兼容
type CVSync2AsyncSubmitTaskRequest = SubmitTaskRequest
type CVSync2AsyncSubmitTaskResponse = SubmitTaskResponse
type CVSync2AsyncGetResultRequest = GetResultRequest
type CVSync2AsyncGetResultResponse = GetResultResponse

// 内部通用函数

// submitTaskInternal 通用提交任务内部函数
func (c *Client) submitTaskInternal(req *SubmitTaskRequest, apiFunc func(interface{}) (map[string]interface{}, int, error)) (*SubmitTaskResponse, int, error) {
	// 构建请求体，确保req_key为常量
	reqBody := make(map[string]interface{})
	for k, v := range req.ReqBody {
		reqBody[k] = v
	}

	var (
		resp   map[string]interface{}
		status int
		err    error
	)

	// 调用传入的API函数
	resp, status, err = apiFunc(reqBody)
	if err != nil {
		return nil, status, fmt.Errorf("API call failed: %w", err)
	}

	// 构建响应
	result := &SubmitTaskResponse{}
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, status, fmt.Errorf("failed to marshal response: %w", err)
	}
	if err := json.Unmarshal(respBytes, result); err != nil {
		return nil, status, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	result.Status = status
	// 如果状态码不是200，将其作为错误处理
	if status != 200 {
		// 尝试从响应中提取错误信息
		if errMsg, exists := resp["error"]; exists {
			result.Error = fmt.Sprintf("%v", errMsg)
		}
		if result.Error == "" {
			result.Error = fmt.Sprintf("HTTP status: %d", status)
		}
	}

	return result, status, nil
}

// getResultInternal 通用获取结果内部函数
func (c *Client) getResultInternal(req *GetResultRequest, apiFunc func(interface{}) (map[string]interface{}, int, error)) (*GetResultResponse, error) {
	// 构建请求体，使用外部传入的req_key
	reqKey := req.ReqKey
	if reqKey == "" {
		reqKey = SeededitReqKey // 如果没有传入则使用默认值
	}
	reqBody := map[string]interface{}{
		"req_key":  reqKey,
		"task_id":  req.TaskID,
		"req_json": `{"return_url":true}`,
	}

	// 调用传入的API函数
	resp, status, err := apiFunc(reqBody)
	if err != nil {
		return nil, fmt.Errorf("API call failed: %w", err)
	}

	// 构建响应
	result := &GetResultResponse{}
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}
	if err := json.Unmarshal(respBytes, result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	result.Status = status
	if status != 200 {
		return nil, fmt.Errorf("HTTP status: %d:%v", status, result.Message)
	}
	if result.Code != 10000 {
		return nil, fmt.Errorf("volcengine code: %d:%v", result.Code, result.Message)
	}
	if result.Data.Status == "in_queue" {
		return nil, nil
	}
	if result.Data.Status == "generating" {
		return nil, nil
	}
	if result.Data.Status == "done" {
		return result, nil
	}
	if result.Data.Status == "not_found" {
		return nil, fmt.Errorf("volcengine status: %s", result.Data.Status)
	}
	if result.Data.Status == "expired" {
		return nil, fmt.Errorf("volcengine status: %s", result.Data.Status)
	}
	return result, nil
}

// CVSync2AsyncSubmitTask 提交异步任务
func (c *Client) CVSync2AsyncSubmitTask(req *CVSync2AsyncSubmitTaskRequest) (*CVSync2AsyncSubmitTaskResponse, int, error) {
	result, code, err := c.submitTaskInternal(req, visual.DefaultInstance.CVSync2AsyncSubmitTask)
	if err != nil {
		return nil, code, err
	}
	if code != 200 {
		return nil, code, fmt.Errorf("volcengine code: %d:%v", code, result.Message)
	}
	// 直接返回结果，因为CVSync2AsyncSubmitTaskResponse是SubmitTaskResponse的别名
	return result, code, nil
}

// CVSync2AsyncGetResult 获取异步任务结果
func (c *Client) CVSync2AsyncGetResult(req *CVSync2AsyncGetResultRequest) (*CVSync2AsyncGetResultResponse, error) {
	return c.getResultInternal(req, visual.DefaultInstance.CVSync2AsyncGetResult)
}

// CVSync2AsyncSubmitTaskWithParams 为提交任务添加额外参数的辅助方法
func (c *Client) CVSync2AsyncSubmitTaskWithParams(extraParams map[string]interface{}) (*CVSync2AsyncSubmitTaskResponse, error) {
	// 构建请求体，使用常量req_key
	reqBody := map[string]interface{}{
		"req_key": SeededitReqKey,
	}

	// 添加额外参数
	for key, value := range extraParams {
		reqBody[key] = value
	}

	// 使用submitTaskInternal函数
	req := &SubmitTaskRequest{
		ReqBody: reqBody,
	}
	result, _, err := c.submitTaskInternal(req, visual.DefaultInstance.CVSync2AsyncSubmitTask)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// CVSubmitTask 提交任务
func (c *Client) CVSubmitTask(req *SubmitTaskRequest) (*SubmitTaskResponse, int, error) {
	return c.submitTaskInternal(req, visual.DefaultInstance.CVSubmitTask)
}

// CVGetResult 获取任务结果
func (c *Client) CVGetResult(req *GetResultRequest) (*GetResultResponse, error) {
	return c.getResultInternal(req, visual.DefaultInstance.CVGetResult)
}

// NewSubmitTaskRequest 创建提交任务请求的辅助函数（通用）
func NewSubmitTaskRequest(params map[string]interface{}) *SubmitTaskRequest {
	return &SubmitTaskRequest{
		ReqBody: params,
	}
}

// NewGetResultRequest 创建获取结果请求的辅助函数（通用）
func NewGetResultRequest(reqKey, taskID string) *GetResultRequest {
	return &GetResultRequest{
		ReqKey: reqKey,
		TaskID: taskID,
	}
}

// FormatResponse 格式化响应为JSON字符串（用于调试）
func FormatResponse(resp interface{}) string {
	b, err := json.Marshal(resp)
	if err != nil {
		return fmt.Sprintf("marshal error: %v", err)
	}
	return string(b)
}
