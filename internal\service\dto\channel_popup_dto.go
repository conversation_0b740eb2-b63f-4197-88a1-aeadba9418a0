package dto

import "time"

// ChannelPopupDto 渠道弹窗关联基础DTO
type ChannelPopupDto struct {
	ID        int       `json:"id"`         // 主键ID
	ChannelID int       `json:"channel_id"` // 渠道ID
	PopupID   int       `json:"popup_id"`   // 弹窗ID
	CreateAt  time.Time `json:"create_at"`  // 创建时间
	UpdateAt  time.Time `json:"update_at"`  // 更新时间

	// 可选的详细信息（当需要时填充）
	Channel *MarketingChannelDto `json:"channel,omitempty"` // 渠道详细信息
	Popup   *PopupDto            `json:"popup,omitempty"`   // 弹窗详细信息
}

// ChannelPopupCreateDto 创建渠道弹窗关联DTO
type ChannelPopupCreateDto struct {
	ChannelID int `json:"channel_id" binding:"required,min=1"` // 渠道ID
	PopupID   int `json:"popup_id" binding:"required,min=1"`   // 弹窗ID
}

// ChannelPopupBatchCreateDto 批量创建渠道弹窗关联DTO
type ChannelPopupBatchCreateDto struct {
	ChannelIDs []int `json:"channel_ids" binding:"required,dive,min=1"` // 渠道ID列表
	PopupIDs   []int `json:"popup_ids" binding:"required,dive,min=1"`   // 弹窗ID列表
}

// ChannelPopupQueryDto 查询渠道弹窗关联DTO
type ChannelPopupQueryDto struct {
	Page             int    `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize         int    `json:"page_size" form:"page_size" binding:"min=1,max=100"` // 每页大小
	ChannelID        int    `json:"channel_id" form:"channel_id"`                       // 渠道ID筛选
	PopupID          int    `json:"popup_id" form:"popup_id"`                           // 弹窗ID筛选
	ChannelTitle     string `json:"channel_title" form:"channel_title"`                 // 渠道标题筛选
	PopupTitle       string `json:"popup_title" form:"popup_title"`                     // 弹窗标题筛选
	ChannelType      *int8  `json:"channel_type" form:"channel_type"`                   // 渠道类型筛选
	PopupLocation    string `json:"popup_location" form:"popup_location"`               // 弹窗位置筛选
	ChannelIsDeleted *int8  `json:"channel_is_deleted" form:"channel_is_deleted"`       // 渠道删除状态筛选：-1未删除，1已删除
	PopupIsDeleted   *int8  `json:"popup_is_deleted" form:"popup_is_deleted"`           // 弹窗删除状态筛选：-1未删除，1已删除
}

// ChannelPopupBatchDeleteDto 批量删除渠道弹窗关联DTO
type ChannelPopupBatchDeleteDto struct {
	ChannelID int `json:"channel_id" binding:"required,min=1"` // 渠道ID
	PopupID   int `json:"popup_id" binding:"required,min=1"`   // 弹窗ID
}
