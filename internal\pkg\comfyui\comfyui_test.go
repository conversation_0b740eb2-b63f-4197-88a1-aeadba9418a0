package comfyui

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestClient_History(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/history/test_prompt" {
			t.<PERSON><PERSON>("Expected to request '/history/test_prompt', got %s", r.URL.Path)
		}
		w.<PERSON><PERSON>ead<PERSON>(http.StatusOK)
		json.NewEncoder(w).Encode(HistoryResponse{})
	}))
	defer server.Close()

	c := NewClient(server.URL)
	_, err := c.History(context.Background(), "test_prompt")
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
}

func TestClient_Prompt(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/prompt" {
			t.<PERSON><PERSON><PERSON>("Expected to request '/prompt', got %s", r.URL.Path)
		}
		w.<PERSON><PERSON><PERSON><PERSON>(http.StatusOK)
		json.NewEncoder(w).Encode(PromptResponse{PromptID: "123"})
	}))
	defer server.Close()

	c := NewClient(server.URL)
	promptData := map[string]interface{}{"key": "value"}
	_, err := c.Prompt(context.Background(), "test_client", promptData)
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
}

func TestClient_UploadImage(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/upload/image" {
			t.Errorf("Expected to request '/upload/image', got %s", r.URL.Path)
		}
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(UploadImageResponse{Name: "test.jpg"})
	}))
	defer server.Close()

	c := NewClient(server.URL)
	fileContent := "fake image content"
	reader := strings.NewReader(fileContent)
	_, err := c.UploadImage(context.Background(), reader, "test.jpg")
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
}

func TestClient_GetPromptQueue(t *testing.T) {

	c := NewClient("https://u565246-9ef4-6e10a9e0.westx.seetacloud.com:8443")
	res, err := c.GetPromptQueue(context.Background())
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	fmt.Println(res)
}

func TestClient_GetHistoryWithStatus(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if strings.HasPrefix(r.URL.Path, "/history/") {
			promptID := strings.TrimPrefix(r.URL.Path, "/history/")
			var response HistoryResponse
			if promptID == "completed_prompt" {
				response = HistoryResponse{
					"completed_prompt": {
						Status: HistoryStatusInfo{StatusStr: "success"},
						Outputs: map[string]HistoryNodeOutput{
							"node_id": {
								Images: []ImageInfo{
									{Filename: "test.jpg", Subfolder: "", Type: "output"},
								},
							},
						},
					},
				}
			} else if promptID == "failed_prompt" {
				response = HistoryResponse{
					"failed_prompt": {
						Status: HistoryStatusInfo{StatusStr: "error", Messages: []interface{}{"error message"}},
					},
				}
			} else {
				response = HistoryResponse{}
			}
			w.WriteHeader(http.StatusOK)
			json.NewEncoder(w).Encode(response)
		} else {
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer server.Close()

	c := NewClient(server.URL)

	// Test running case
	resp, err := c.GetHistoryWithStatus(context.Background(), "running_prompt")
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	if resp.Status != "running" {
		t.Errorf("expected status 'running', got '%s'", resp.Status)
	}

	// Test completed case
	resp, err = c.GetHistoryWithStatus(context.Background(), "completed_prompt")
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	if resp.Status != "completed" {
		t.Errorf("expected status 'completed', got '%s'", resp.Status)
	}

	// Test failed case
	resp, err = c.GetHistoryWithStatus(context.Background(), "failed_prompt")
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	if resp.Status != "failed" {
		t.Errorf("expected status 'failed', got '%s'", resp.Status)
	}
}
