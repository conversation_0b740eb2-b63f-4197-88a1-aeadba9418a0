package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	"chongli/internal/service"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
)

type GoodsController struct {
	log           *logger.Logger
	goodsBiz      *biz.GoodsBiz
	configService *service.ConfigService
}

func NewGoodsController(
	bootstrap *component.BootStrap,
	GoodsBiz *biz.GoodsBiz,
	configService *service.ConfigService,
) *GoodsController {
	return &GoodsController{
		log:           bootstrap.Log,
		goodsBiz:      GoodsBiz,
		configService: configService,
	}
}

type GoodsListResponse struct {
	// Id            int     `json:"id"`
	GoodsId       string  `json:"goods_id"`
	GoodsMiddleId string  `json:"goods_middle_id"`
	Diamond       int     `json:"diamond"`
	Title         string  `json:"title"`
	Price         float64 `json:"price"`
	Sort          int     `json:"sort"`
	ChannelId     int     `json:"channel_id"`
	ChannelTitle  string  `json:"channel_title"`
	PerWorkCost   float64 `json:"per_work_cost"`
}

// GetGoodsList 获取商品列表
func (c *GoodsController) GetGoodsList(ctx *gin.Context) {
	header := utils.RequestHeader(ctx)
	if len(header.DeviceId) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("DeviceID是必须的"), response.WithSLSLog)
		return
	}
	if len(header.Channel) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("Channel是必须的"), response.WithSLSLog)
		return
	}
	if header.Version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("Version是必须的"), response.WithSLSLog)
		return
	}

	req := biz.Goods{
		RequestHeaderDto: *header,
	}
	goodsList, err := c.goodsBiz.GoodsList(&req)
	if err != nil {
		c.log.Error("获取商品列表失败: %v", err.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取商品列表失败"), response.WithSLSLog)
		return
	}

	// 获取配置的默认制作花费
	defaultSpendStr, err := c.configService.GetConfigByKeyFromCache(ctx, "default_make_spend")
	if err != nil {
		c.log.Error("获取默认制作花费配置失败: %v", err.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("获取默认制作花费配置失败"), response.WithSLSLog)
		return
	}
	defaultSpend, err := strconv.ParseInt(defaultSpendStr, 10, 64)
	if err != nil {
		c.log.Error("解析默认制作花费配置失败: %v", err.Error())
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("解析默认制作花费配置失败"), response.WithSLSLog)
		return
	}

	res := make([]GoodsListResponse, 0)
	for _, v := range goodsList {
		goods := GoodsListResponse{
			GoodsId:       strconv.Itoa(v.VipGoodsInfoDto.Id),
			GoodsMiddleId: v.VipGoodsInfoDto.GoodsId,
			Diamond:       v.VipGoodsInfoDto.MonthlyDiamond,
			Title:         v.VipGoodsInfoDto.Title,
			Price:         v.VipGoodsInfoDto.Price,
			Sort:          v.VipGoodsInfoDto.Sort,
			ChannelId:     v.MarketingChannel.ID,
			ChannelTitle:  v.MarketingChannel.Title,
			PerWorkCost: math.Round(
				(v.VipGoodsInfoDto.Price/
					(float64(v.VipGoodsInfoDto.MonthlyDiamond)*
						c.getGoodsVipGiveCount(v.VipGoodsInfoDto.VipType)/
						float64(defaultSpend)))*100) / 100,
		}
		res = append(res, goods)
	}
	response.Response(ctx, nil, res, nil, response.WithSLSLog)
}

func (c *GoodsController) getGoodsVipGiveCount(vipType int) float64 {
	switch vipType {
	case 1: // 月
		return 1
	case 2: // 季
		return 6
	case 3: // 年
		return 12
	default:
		return 1
	}
}
