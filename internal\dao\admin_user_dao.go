package dao

import (
	"chongli/component"
	"chongli/internal/app/admin/dto"
	"chongli/internal/model"
	"chongli/pkg/logger"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type AdminUserDao struct {
	log *logger.Logger
	db  *gorm.DB
	rds *redis.Client
}

func NewAdminUserDao(bootStrap *component.BootStrap) *AdminUserDao {
	return &AdminUserDao{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

// ===== AdminUser 增删改查 =====

// CreateAdminUser 创建后台用户
func (d *AdminUserDao) CreateAdminUser(user *model.AdminUser) error {
	err := d.db.Create(user).Error
	if err != nil {
		d.log.Error("创建后台用户失败: %v", err)
		return err
	}
	return nil
}

// GetAdminUserByID 根据ID获取后台用户
func (d *AdminUserDao) GetAdminUserByID(id int64) (*model.AdminUser, error) {
	var user model.AdminUser
	err := d.db.Where("id = ?", id).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询后台用户失败: %v", err)
		return nil, err
	}
	return &user, nil
}

// GetAdminUserByCondition 根据条件获取后台用户
func (d *AdminUserDao) GetAdminUserByCondition(condition map[string]interface{}) (*model.AdminUser, error) {
	var user model.AdminUser
	err := d.db.Where(condition).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询后台用户失败: %v", err)
		return nil, err
	}
	return &user, nil
}

// GetAdminUserList 获取后台用户列表
func (d *AdminUserDao) GetAdminUserList(condition map[string]interface{}, offset, limit int) ([]*model.AdminUser, int64, error) {
	var users []*model.AdminUser
	var total int64

	query := d.db.Model(&model.AdminUser{})

	if condition != nil {
		query = query.Where(condition)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		d.log.Error("查询后台用户总数失败: %v", err)
		return nil, 0, err
	}

	// 获取列表
	err = query.Offset(offset).Limit(limit).Find(&users).Error
	if err != nil {
		d.log.Error("查询后台用户列表失败: %v", err)
		return nil, 0, err
	}

	return users, total, nil
}

// UpdateAdminUser 更新后台用户
func (d *AdminUserDao) UpdateAdminUser(user *model.AdminUser) error {
	err := d.db.Save(user).Error
	if err != nil {
		d.log.Error("更新后台用户失败: %v", err)
		return err
	}
	return nil
}

// UpdateAdminUserByCondition 根据条件更新后台用户
func (d *AdminUserDao) UpdateAdminUserByCondition(condition map[string]interface{}, updates map[string]interface{}) error {
	err := d.db.Model(&model.AdminUser{}).Where(condition).Updates(updates).Error
	if err != nil {
		d.log.Error("更新后台用户失败: %v", err)
		return err
	}
	return nil
}

// DeleteAdminUser 删除后台用户（软删除）
func (d *AdminUserDao) DeleteAdminUser(id int64) error {
	err := d.db.Model(&model.AdminUser{}).Where("id = ?", id).Update("is_delete", model.StatusEnabled).Error
	if err != nil {
		d.log.Error("删除后台用户失败: %v", err)
		return err
	}
	return nil
}

// ===== EmailVerifyCode 增删改查 =====

// CreateEmailVerifyCode 创建邮箱验证码
func (d *AdminUserDao) CreateEmailVerifyCode(code *model.EmailVerifyCode) error {
	err := d.db.Create(code).Error
	if err != nil {
		d.log.Error("创建邮箱验证码失败: %v", err)
		return err
	}
	return nil
}

// GetEmailVerifyCodeByCondition 根据条件获取邮箱验证码
func (d *AdminUserDao) GetEmailVerifyCodeByCondition(condition map[string]interface{}) (*model.EmailVerifyCode, error) {
	var code model.EmailVerifyCode
	err := d.db.Where(condition).First(&code).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询邮箱验证码失败: %v", err)
		return nil, err
	}
	return &code, nil
}

// UpdateEmailVerifyCode 更新邮箱验证码
func (d *AdminUserDao) UpdateEmailVerifyCode(code *model.EmailVerifyCode) error {
	err := d.db.Save(code).Error
	if err != nil {
		d.log.Error("更新邮箱验证码失败: %v", err)
		return err
	}
	return nil
}

// UpdateEmailVerifyCodeByCondition 根据条件更新邮箱验证码
func (d *AdminUserDao) UpdateEmailVerifyCodeByCondition(condition map[string]interface{}, updates map[string]interface{}) error {
	err := d.db.Model(&model.EmailVerifyCode{}).Where(condition).Updates(updates).Error
	if err != nil {
		d.log.Error("更新邮箱验证码失败: %v", err)
		return err
	}
	return nil
}

// ===== AdminLoginLog 增删改查 =====

// CreateAdminLoginLog 创建登录日志
func (d *AdminUserDao) CreateAdminLoginLog(log *model.AdminLoginLog) error {
	err := d.db.Create(log).Error
	if err != nil {
		d.log.Error("创建登录日志失败: %v", err)
		return err
	}
	return nil
}

// GetAdminLoginLogList 获取登录日志列表
func (d *AdminUserDao) GetAdminLoginLogList(condition map[string]interface{}, offset, limit int) ([]*model.AdminLoginLog, int64, error) {
	var logs []*model.AdminLoginLog
	var total int64

	query := d.db.Model(&model.AdminLoginLog{})

	if condition != nil {
		query = query.Where(condition)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		d.log.Error("查询登录日志总数失败: %v", err)
		return nil, 0, err
	}

	// 获取列表
	err = query.Order("create_at DESC").Offset(offset).Limit(limit).Find(&logs).Error
	if err != nil {
		d.log.Error("查询登录日志列表失败: %v", err)
		return nil, 0, err
	}

	return logs, total, nil
}

// ===== Redis 操作 =====

// SetRedis 设置Redis键值
func (d *AdminUserDao) SetRedis(key string, value interface{}, expiration time.Duration) error {
	err := d.rds.Set(d.rds.Context(), key, value, expiration).Err()
	if err != nil {
		d.log.Error("设置Redis失败: %v", err)
		return err
	}
	return nil
}

// GetRedis 获取Redis值
func (d *AdminUserDao) GetRedis(key string) (string, error) {
	value, err := d.rds.Get(d.rds.Context(), key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return "", nil
		}
		d.log.Error("获取Redis失败: %v", err)
		return "", err
	}
	return value, nil
}

// DeleteRedis 删除Redis键
func (d *AdminUserDao) DeleteRedis(key string) error {
	err := d.rds.Del(d.rds.Context(), key).Err()
	if err != nil {
		d.log.Error("删除Redis键失败: %v", err)
		return err
	}
	return nil
}

// ExistsRedis 检查Redis键是否存在
func (d *AdminUserDao) ExistsRedis(key string) (bool, error) {
	exists, err := d.rds.Exists(d.rds.Context(), key).Result()
	if err != nil {
		d.log.Error("检查Redis键存在性失败: %v", err)
		return false, err
	}
	return exists > 0, nil
}

// IncrRedis Redis自增
func (d *AdminUserDao) IncrRedis(key string) (int64, error) {
	value, err := d.rds.Incr(d.rds.Context(), key).Result()
	if err != nil {
		d.log.Error("Redis自增失败: %v", err)
		return 0, err
	}
	return value, nil
}

// ExpireRedis 设置Redis键过期时间
func (d *AdminUserDao) ExpireRedis(key string, expiration time.Duration) error {
	err := d.rds.Expire(d.rds.Context(), key, expiration).Err()
	if err != nil {
		d.log.Error("设置Redis过期时间失败: %v", err)
		return err
	}
	return nil
}

// ===== 工具函数 =====

// ConvertToAdminUserDto 将model转换为dto
func (d *AdminUserDao) ConvertToAdminUserDto(user *model.AdminUser) *dto.AdminUserDto {
	if user == nil {
		return nil
	}

	return &dto.AdminUserDto{
		ID:            user.ID,
		Email:         user.Email,
		Nickname:      user.Nickname,
		Avatar:        user.Avatar,
		LastLoginTime: user.LastLoginTime,
		LastLoginIP:   user.LastLoginIP,
		LoginCount:    user.LoginCount,
		CreatedBy:     user.CreatedBy,
		UpdatedBy:     user.UpdatedBy,
		CreateAt:      user.CreateAt,
		UpdateAt:      user.UpdateAt,
		IsDelete:      user.IsDelete,
		Status:        model.StatusEnabled, // 向后兼容：始终返回启用状态
	}
}
