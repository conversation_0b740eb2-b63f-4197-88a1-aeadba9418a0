package biz

import (
	"chongli/component"
	"chongli/internal/dao"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"github.com/gin-gonic/gin"
	"time"
)

type TemplateBiz struct {
	log             *logger.Logger
	templateRepo    repo.TemplateAIRepo
	templateLogRepo repo.TemplateViewMakeLogRepo
}

func NewTemplateBiz(
	bootStrap *component.BootStrap,
	templateRepo repo.TemplateAIRepo,
) *TemplateBiz {
	return &TemplateBiz{
		log:             bootStrap.Log,
		templateRepo:    templateRepo,
		templateLogRepo: dao.NewTemplateViewMakeLogRepo(bootStrap),
	}
}

func (t *TemplateBiz) GetMainClassTemplateList(ctx *gin.Context, templateDto *dto.TemplateAIQueryDTO) ([]*dto.TemplateAIDTO, error) {
	// 获取主分类下模板列表
	templates, err := t.templateRepo.ListByQuery(ctx, templateDto)
	if err != nil {
		t.log.Error("获取主分类下模板列表失败: %v", err)
		return nil, err
	}
	if len(templates) == 0 {
		t.log.Warning("该主分类没有模板")
		return make([]*dto.TemplateAIDTO, 0), nil
	}
	// 返回模板列表
	return templates, nil
}

func (t *TemplateBiz) GetCategoryTemplateList(ctx *gin.Context, templateDto *dto.TemplateAIQueryDTO) ([]*dto.TemplateAIDTO, error) {
	// 获取模板列表
	templates, err := t.templateRepo.ListByQuery(ctx, templateDto)
	if err != nil {
		t.log.Error("获取模板列表失败: %v", err)
		return nil, err
	}
	if len(templates) == 0 {
		t.log.Warning("该分类没有模板")
		return make([]*dto.TemplateAIDTO, 0), nil
	}
	// 返回模板列表
	return templates, nil
}

func (t *TemplateBiz) GetTemplateDetail(ctx *gin.Context, templateId uint64) (*dto.TemplateAIDTO, error) {
	// 获取模板详情
	template, err := t.templateRepo.GetByID(ctx, templateId)
	if err != nil {
		t.log.Error("获取模板详情失败: %v", err)
		return nil, err
	}
	if template == nil {
		t.log.Warning("模板不存在: %d", templateId)
		return nil, nil
	}
	// 返回模板详情
	return template, nil
}

// IncreaseView 增加模板浏览量（记录一条日志）
func (t *TemplateBiz) IncreaseView(ctx *gin.Context, templateId uint64) error {
	if templateId == 0 {
		return nil
	}

	log := &dto.TemplateViewMakeLogDTO{
		TemplateID: int64(templateId),
		Type:       model.ViewTemplateType,
		CreateAt:   time.Now().Unix(),
	}

	if err := t.templateLogRepo.Create(ctx, log); err != nil {
		t.log.Error("记录模板浏览量失败: %v", err)
		return err
	}

	return nil
}
