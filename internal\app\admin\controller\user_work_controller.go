package controller

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
)

type UserWorkController struct {
	log          *logger.Logger
	userWorkRepo repo.UserWorkRepo
	taskStepRepo repo.TaskStepRepo
}

func NewUserWorkController(
	bootStrap *component.BootStrap,
	userWorkRepo repo.UserWorkRepo,
	taskStepRepo repo.TaskStepRepo,
) *UserWorkController {
	return &UserWorkController{
		log:          bootStrap.Log,
		userWorkRepo: userWorkRepo,
		taskStepRepo: taskStepRepo,
	}
}

// UserWorkListRequest 用户作品列表请求
type UserWorkListRequest struct {
	Page          int    `form:"page" binding:"required,min=1"`
	PageSize      int    `form:"page_size"`
	ID            uint64 `form:"id"`
	UserID        uint64 `form:"user_id"`
	WorkType      string `form:"work_type"` // pic, dance, sing
	Status        *int8  `form:"status"`    // -1: 全部, 0: 制作中, 1: 成功, 2: 失败
	Username      string `form:"username"`
	TemplateID    uint64 `form:"template_id"`
	BeginCreateAt string `form:"begin_create_at"`
	EndCreateAt   string `form:"end_create_at"`
}

// UserWorkListResponse 用户作品列表响应
type UserWorkListResponse struct {
	ID                 uint64 `json:"id"`
	UserID             uint64 `json:"user_id"`
	Username           string `json:"username"`
	WorkType           string `json:"work_type"`
	Cover              string `json:"cover"`
	TemplateID         uint64 `json:"template_id"`
	TemplateName       string `json:"template_name"`
	TemplateCover      string `json:"template_cover"`
	Status             int8   `json:"status"`
	StatusText         string `json:"status_text"`
	ErrMsg             string `json:"error_msg"`
	CreateTime         string `json:"create_time"`
	PicURL             string `json:"pic_url"`
	VideoURL           string `json:"video_url"`
	Diamond            int64  `json:"diamond"`
	ExpectedFinishTime string `json:"expected_finish_time"`
	IsDeleted          int8   `json:"is_deleted"`
}

// UpdateUserWorkRequest 更新用户作品请求
type UpdateUserWorkRequest struct {
	ID       uint64 `json:"id" binding:"required"`
	Status   int8   `json:"status"`
	ErrMsg   string `json:"error_msg"`
	PicURL   string `json:"pic_url"`
	VideoURL string `json:"video_url"`
}

// BatchDeleteUserWorkRequest 批量删除用户作品请求
type BatchDeleteUserWorkRequest struct {
	IDs []uint64 `json:"ids" binding:"required,min=1"`
}

// getStatusText 获取状态文本
func getStatusText(status int8) string {
	switch status {
	case 0:
		return "制作中"
	case 1:
		return "成功"
	case 2:
		return "失败"
	default:
		return "未知"
	}
}

// UserWorkList 获取用户作品列表
func (c *UserWorkController) UserWorkList(ctx *gin.Context) {
	var req UserWorkListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	// 设置默认分页大小
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 构造查询条件
	query := &dto.UserWorks{
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	if req.ID > 0 {
		query.ID = req.ID
	}

	if req.UserID > 0 {
		query.UserID = req.UserID
	}

	if req.WorkType != "" {
		query.WorkType = req.WorkType
	}

	if req.Status != nil {
		query.Status = model.StatusFlag(*req.Status)
	}

	// if req.Username != "" {
	// 	query.Username = req.Username
	// }

	if req.TemplateID > 0 {
		query.TemplateID = req.TemplateID
	}

	// 查询数据
	list, err := c.userWorkRepo.List(ctx, query, true)
	if err != nil {
		c.log.Error("获取用户作品列表失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("获取用户作品列表失败"), response.WithSLSLog)
		return
	}

	// 获取总数
	total, err := c.userWorkRepo.Count(ctx, &dto.UserWorks{
		UserID:   query.UserID,
		WorkType: query.WorkType,
		Status:   query.Status,
	})
	if err != nil {
		c.log.Error("获取用户作品总数失败: %v", err)
		total = 0
	}

	// 转换响应数据
	var output []*UserWorkListResponse
	for _, item := range list {
		var expectedFinishTime string
		if item.ExpectedFinishTime != nil {
			expectedFinishTime = item.ExpectedFinishTime.Format("2006-01-02 15:04:05")
		}

		username := ""
		if item.User != nil {
			username = item.User.Username
		}

		templateName := ""
		if item.Template != nil {
			templateName = item.Template.Name
		}

		templateCover := ""
		if item.Template != nil {
			if item.Template.VideoCoverURL != "" {
				templateCover = utils.EnsureHttpsPrefix(item.Template.VideoCoverURL)
			} else if item.Template.CoverURL != "" {
				templateCover = utils.EnsureHttpsPrefix(item.Template.CoverURL)
			}
		}

		output = append(output, &UserWorkListResponse{
			ID:                 item.ID,
			UserID:             item.UserID,
			Username:           username,
			WorkType:           item.WorkType,
			Cover:              utils.EnsureHttpsPrefix(item.Cover),
			TemplateID:         item.TemplateID,
			TemplateName:       templateName,
			TemplateCover:      templateCover,
			Status:             int8(item.Status),
			StatusText:         getStatusText(int8(item.Status)),
			ErrMsg:             item.ErrMsg,
			CreateTime:         item.CreateAt.Format("2006-01-02 15:04:05"),
			PicURL:             utils.EnsureHttpsPrefix(item.PicURL),
			VideoURL:           utils.EnsureHttpsPrefix(item.VideoURL),
			Diamond:            item.Diamond,
			ExpectedFinishTime: expectedFinishTime,
			IsDeleted:          int8(item.IsDeleted),
		})
	}

	responseData := map[string]interface{}{
		"list":      output,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	}

	response.Response(ctx, nil, responseData, nil, response.WithSLSLog)
}

// UpdateUserWork 更新用户作品
func (c *UserWorkController) UpdateUserWork(ctx *gin.Context) {
	var req UpdateUserWorkRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	// 先查询作品是否存在
	work, err := c.userWorkRepo.GetOne(ctx, &dto.UserWorks{ID: req.ID})
	if err != nil {
		c.log.Error("查询用户作品失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("作品不存在"), response.WithSLSLog)
		return
	}

	if work == nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("作品不存在"), response.WithSLSLog)
		return
	}

	// 构造更新数据
	updates := make(map[string]interface{})

	// 状态更新
	if req.Status >= 0 {
		updates["status"] = req.Status
	}

	// 错误信息更新
	if req.ErrMsg != "" {
		updates["error_msg"] = req.ErrMsg
	}

	// 图片URL更新
	if req.PicURL != "" {
		updates["pic_url"] = req.PicURL
	}

	// 视频URL更新
	if req.VideoURL != "" {
		updates["video_url"] = req.VideoURL
	}

	if len(updates) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewLowError("没有需要更新的字段"), response.WithSLSLog)
		return
	}

	// 执行更新
	if err := c.userWorkRepo.Update(ctx, req.ID, updates); err != nil {
		c.log.Error("更新用户作品失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("更新失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, "更新成功", nil, response.WithSLSLog)
}

// DeleteUserWork 删除用户作品
func (c *UserWorkController) DeleteUserWork(ctx *gin.Context) {
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Response(ctx, nil, nil, errpkg.NewLowError("缺少id参数"), response.WithSLSLog)
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("id参数格式错误"), response.WithSLSLog)
		return
	}

	// 先查询作品是否存在
	work, err := c.userWorkRepo.GetOne(ctx, &dto.UserWorks{ID: id})
	if err != nil {
		c.log.Error("查询用户作品失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("作品不存在"), response.WithSLSLog)
		return
	}

	if work == nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("作品不存在"), response.WithSLSLog)
		return
	}

	if work.IsDeleted == model.StatusEnabled {
		response.Response(ctx, nil, nil, errpkg.NewLowError("作品已被删除"), response.WithSLSLog)
		return
	}

	// 执行软删除
	if err := c.userWorkRepo.Delete(ctx, &dto.UserWorks{ID: id}); err != nil {
		c.log.Error("删除用户作品失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("删除失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, "删除成功", nil, response.WithSLSLog)
}

// BatchDeleteUserWork 批量删除用户作品
func (c *UserWorkController) BatchDeleteUserWork(ctx *gin.Context) {
	var req BatchDeleteUserWorkRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	if len(req.IDs) == 0 {
		response.Response(ctx, nil, nil, errpkg.NewLowError("请选择要删除的作品"), response.WithSLSLog)
		return
	}

	// 限制批量删除数量，防止操作过大
	if len(req.IDs) > 100 {
		response.Response(ctx, nil, nil, errpkg.NewLowError("一次最多删除100个作品"), response.WithSLSLog)
		return
	}

	var successCount int
	var failedCount int
	var failedIDs []uint64

	// 循环删除每个作品
	for _, id := range req.IDs {
		// 先查询作品是否存在
		work, err := c.userWorkRepo.GetOne(ctx, &dto.UserWorks{ID: id})
		if err != nil {
			c.log.Error("查询用户作品失败: ID=%d, error=%v", id, err)
			failedCount++
			failedIDs = append(failedIDs, id)
			continue
		}

		if work == nil {
			c.log.Error("作品不存在: ID=%d", id)
			failedCount++
			failedIDs = append(failedIDs, id)
			continue
		}

		if work.IsDeleted == model.StatusEnabled {
			c.log.Error("作品已被删除: ID=%d", id)
			failedCount++
			failedIDs = append(failedIDs, id)
			continue
		}

		// 执行软删除
		if err := c.userWorkRepo.Delete(ctx, &dto.UserWorks{ID: id}); err != nil {
			c.log.Error("删除用户作品失败: ID=%d, error=%v", id, err)
			failedCount++
			failedIDs = append(failedIDs, id)
			continue
		}

		successCount++
	}

	// 构造响应消息
	var message string
	if failedCount == 0 {
		message = fmt.Sprintf("批量删除成功，共删除%d个作品", successCount)
	} else {
		message = fmt.Sprintf("批量删除完成，成功删除%d个作品，失败%d个作品", successCount, failedCount)
	}

	responseData := map[string]interface{}{
		"message":       message,
		"success_count": successCount,
		"failed_count":  failedCount,
		"failed_ids":    failedIDs,
	}

	response.Response(ctx, nil, responseData, nil, response.WithSLSLog)
}

func (c *UserWorkController) GetUserWorkTaskSteps(ctx *gin.Context) {
	idStr := ctx.Param("id")
	if idStr == "" {
		response.Response(ctx, nil, nil, errpkg.NewLowError("缺少id参数"), response.WithSLSLog)
		return
	}

	workId, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("id参数格式错误"), response.WithSLSLog)
		return
	}

	// 先查询作品是否存在
	work, err := c.userWorkRepo.GetOne(ctx, &dto.UserWorks{ID: workId})
	if err != nil {
		c.log.Error("查询用户作品失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("作品不存在"), response.WithSLSLog)
		return
	}

	if work == nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError("作品不存在"), response.WithSLSLog)
		return
	}

	// 查询作品的所有 task step
	taskSteps, err := c.taskStepRepo.GetTaskStepByWorkId(ctx, int64(workId))
	if err != nil {
		c.log.Error("查询用户作品任务步骤失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError("查询任务步骤失败"), response.WithSLSLog)
		return
	}

	response.Response(ctx, nil, taskSteps, nil, response.WithSLSLog)
}
