package task

import (
	"chongli/internal/app/task/processors"
	"chongli/internal/app/task/registry"

	"github.com/google/wire"
)

// TimerSet 定时任务依赖注入
var TimerSet = wire.NewSet(
	registry.NewUserTask,
	registry.NewPayOrderTask,
	registry.NewPicTask,
)

// ServiceSet 服务层依赖注入
var ServiceSet = wire.NewSet(
	processors.NewUserTaskProcessors,
	processors.NewPayOrderTaskProcessors,
	processors.NewPicTaskProcessors,
	processors.NewDanceTaskProcessors,
	processors.NewAvatarPictureProcessors,
)

// Set 所有依赖注入
var Set = wire.NewSet(
	TimerSet,
	ServiceSet,
)
