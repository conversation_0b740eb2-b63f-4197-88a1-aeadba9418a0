package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
)

type userVipRepo struct {
	log *logger.Logger
	db  *gorm.DB
}

func NewUserVipRepo(bootStrap *component.BootStrap) repo.UserVipRepo {
	return &userVipRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
	}
}

func (d *userVipRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// convertVipModel2Dto 转换VIP模型为DTO
func (d *userVipRepo) convertVipModel2Dto(vip *model.UserVip) *dto.UserVipDto {
	return &dto.UserVipDto{
		ID:         vip.ID,
		UserId:     vip.UserId,
		VipType:    vip.VipType,
		CreateAt:   vip.CreateAt,
		RenewalAt:  vip.RenewalAt,
		ExpireAt:   vip.ExpireAt,
		IsExpire:   vip.IsExpire,
		IsDelete:   vip.IsDelete,
		OldVipInfo: vip.OldVipInfo,
	}
}

// convertVipGiveModel2Dto 转换VIP赠送模型为DTO
func (d *userVipRepo) convertVipGiveModel2Dto(give *model.UserVipGive) *dto.UserVipGiveDto {
	return &dto.UserVipGiveDto{
		ID:       give.ID,
		UserId:   give.UserId,
		OrderId:  give.OrderId,
		GoodsId:  give.GoodsId,
		VipType:  give.VipType,
		GiveAt:   give.GiveAt,
		GiveNum:  give.GiveNum,
		IsGive:   give.IsGive,
		IsDelete: give.IsDelete,
		CreateAt: give.CreateAt,
	}
}

// calculateExpireTime 计算VIP到期时间
func (d *userVipRepo) calculateExpireTime(giveCount int64, baseTime time.Time) time.Time {
	if giveCount <= 0 {
		// 如果赠送次数为0或负数，默认设置为1个月后
		return baseTime.AddDate(0, 1, 0)
	}
	return baseTime.AddDate(0, int(giveCount), 0)
}

// GetUserVipByUserId 根据用户ID查询VIP信息
func (d *userVipRepo) GetUserVipByUserId(userId int64, tx ...*gorm.DB) (*dto.UserVipDto, error) {
	var vip model.UserVip
	if err := d.getDb(tx).Model(&model.UserVip{}).Where(map[string]any{
		"user_id":   userId,
		"is_delete": model.StatusDisabled,
	}).First(&vip).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询用户VIP信息错误: %v", err.Error())
		return nil, err
	}
	return d.convertVipModel2Dto(&vip), nil
}

// CreateUserVip 开通VIP
func (d *userVipRepo) CreateUserVip(req *dto.CreateAndRenewUserVipRequest, tx ...*gorm.DB) (*dto.UserVipDto, error) {
	now := time.Now()
	expireAt := d.calculateExpireTime(req.GiveCount, now)

	vip := &model.UserVip{
		UserId:   req.UserId,
		VipType:  req.VipType,
		CreateAt: now,
		ExpireAt: expireAt,
		IsExpire: model.NotExpire, // 未过期
		IsDelete: int8(model.StatusDisabled),
	}

	if err := d.getDb(tx).Model(&model.UserVip{}).Create(vip).Error; err != nil {
		d.log.Error("创建用户VIP错误: %v", err.Error())
		return nil, err
	}

	return d.convertVipModel2Dto(vip), nil
}

// RenewUserVip 续费VIP
func (d *userVipRepo) RenewUserVip(req *dto.CreateAndRenewUserVipRequest, tx ...*gorm.DB) error {
	now := time.Now()

	// 查询现有VIP信息
	var vip model.UserVip
	err := d.getDb(tx).Model(&model.UserVip{}).Where(map[string]any{
		"user_id":   req.UserId,
		"is_delete": model.StatusDisabled,
	}).First(&vip).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没有VIP记录，创建新的
			return d.createNewVipRecord(req, now, tx...)
		}
		d.log.Error("查询用户VIP信息错误: %v", err.Error())
		return err
	}

	// 检查VIP是否已过期
	if vip.ExpireAt.Before(now) {
		// VIP已过期，创建新记录
		return d.createNewVipRecord(req, now, tx...)
	}

	// VIP未过期，从当前到期时间开始计算
	newExpireAt := d.calculateExpireTime(req.GiveCount, vip.ExpireAt)

	// 保持最大vip类型
	newVipType := vip.VipType
	if req.VipType > newVipType {
		newVipType = req.VipType
	}

	// 获取旧会员信息
	var oldVipInfoList []*dto.UserVipDto
	if vip.OldVipInfo != "" {
		if err := json.Unmarshal([]byte(vip.OldVipInfo), &oldVipInfoList); err != nil {
			d.log.Error("解析旧会员信息错误: %v", err.Error())
			return err
		}
	} else {
		oldVipInfoList = make([]*dto.UserVipDto, 0)
	}

	// 将当前旧VIP信息添加到旧会员信息列表中
	oldVipInfoList = append(oldVipInfoList, d.convertVipModel2Dto(&vip))

	// 将旧会员信息列表转换为JSON字符串
	oldVipInfoJson, err := json.Marshal(oldVipInfoList)
	if err != nil {
		d.log.Error("序列化旧会员信息错误: %v", err.Error())
		return err
	}

	// 更新VIP信息
	updates := map[string]any{
		"vip_type":     newVipType,
		"renewal_at":   now,
		"expire_at":    newExpireAt,
		"is_expire":    model.NotExpire, // 续费后设为未过期
		"old_vip_info": string(oldVipInfoJson),
	}

	if err := d.getDb(tx).Model(&model.UserVip{}).Where("id = ?", vip.ID).Updates(updates).Error; err != nil {
		d.log.Error("续费用户VIP错误: %v", err.Error())
		return err
	}

	return nil
}

// createNewVipRecord 创建新的VIP记录
func (d *userVipRepo) createNewVipRecord(req *dto.CreateAndRenewUserVipRequest, now time.Time, tx ...*gorm.DB) error {
	expireAt := d.calculateExpireTime(req.GiveCount, now)

	vip := &model.UserVip{
		UserId:    req.UserId,
		VipType:   req.VipType,
		CreateAt:  now,
		RenewalAt: &now, // 续费时设置续费时间
		ExpireAt:  expireAt,
		IsExpire:  model.NotExpire, // 未过期
		IsDelete:  int8(model.StatusDisabled),
	}

	if err := d.getDb(tx).Model(&model.UserVip{}).Create(vip).Error; err != nil {
		d.log.Error("创建新VIP记录错误: %v", err.Error())
		return err
	}

	return nil
}

// UpdateUserVipIsExpireAndIsDelete 更新VIP为过期并删除
func (d *userVipRepo) UpdateUserVipIsExpireAndIsDelete(userId int64, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.UserVip{}).Where(map[string]any{
		"user_id":   userId,
		"is_delete": model.StatusDisabled,
	}).Updates(map[string]any{
		"is_expire": model.IsExpire,
		"is_delete": model.StatusEnabled,
	}).Error; err != nil {
		d.log.Error("更新用户VIP过期状态错误: %v", err.Error())
		return err
	}
	return nil
}

// CreateVipGive 创建VIP赠送记录
func (d *userVipRepo) CreateVipGive(req *dto.CreateVipGiveRequest, tx ...*gorm.DB) (*dto.UserVipGiveDto, error) {
	now := time.Now()

	// 如果没有指定赠送时间，使用当前时间
	giveAt := req.GiveAt
	if giveAt.IsZero() {
		giveAt = now
	}

	give := &model.UserVipGive{
		UserId:   req.UserId,
		GoodsId:  req.GoodsId,
		OrderId:  req.OrderId,
		VipType:  req.VipType,
		GiveAt:   giveAt,
		GiveNum:  req.GiveNum,
		IsGive:   model.GiveStatusPending, // 未赠送
		IsDelete: int8(model.StatusDisabled),
		CreateAt: now,
	}

	if err := d.getDb(tx).Model(&model.UserVipGive{}).Create(give).Error; err != nil {
		d.log.Error("创建VIP赠送记录错误: %v", err.Error())
		return nil, err
	}

	return d.convertVipGiveModel2Dto(give), nil
}

// UpdateVipGiveStatus 更新赠送状态
func (d *userVipRepo) UpdateVipGiveStatus(id int64, isGive int8, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.UserVipGive{}).Where("id = ?", id).Update("is_give", isGive).Error; err != nil {
		d.log.Error("更新VIP赠送状态错误: %v", err.Error())
		return err
	}
	return nil
}

// GetVipGiveById 根据ID查询赠送记录
func (d *userVipRepo) GetVipGiveById(id int64, tx ...*gorm.DB) (*dto.UserVipGiveDto, error) {
	var give model.UserVipGive
	if err := d.getDb(tx).Model(&model.UserVipGive{}).Where("id = ?", id).First(&give).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询VIP赠送记录错误: %v", err.Error())
		return nil, err
	}
	return d.convertVipGiveModel2Dto(&give), nil
}

// GetPendingVipGivesByUserId 获取用户待赠送的记录
func (d *userVipRepo) GetPendingVipGivesByUserId(userId int64, tx ...*gorm.DB) ([]*dto.UserVipGiveDto, error) {
	var gives []*model.UserVipGive
	if err := d.getDb(tx).Model(&model.UserVipGive{}).Where(map[string]any{
		"user_id":   userId,
		"is_give":   model.GiveStatusPending,
		"is_delete": model.StatusDisabled,
	}).Find(&gives).Error; err != nil {
		d.log.Error("查询用户待赠送记录错误: %v", err.Error())
		return nil, err
	}

	var dtoList []*dto.UserVipGiveDto
	for _, give := range gives {
		dtoList = append(dtoList, d.convertVipGiveModel2Dto(give))
	}

	return dtoList, nil
}

// PageVipGive 分页查询VIP赠送记录
func (d *userVipRepo) PageVipGive(req *dto.VipGivePageRequest) (*dto.VipGivePageResponse, error) {
	var gives []*model.UserVipGive
	var total int64

	query := d.db.Model(&model.UserVipGive{})

	// 添加查询条件
	if req.UserId != 0 {
		query = query.Where("user_id = ?", req.UserId)
	}

	if req.GoodsId != 0 {
		query = query.Where("goods_id = ?", req.GoodsId)
	}

	if req.OrderId != "" {
		query = query.Where("order_id = ?", req.OrderId)
	}

	if req.VipType != 0 {
		query = query.Where("vip_type = ?", req.VipType)
	}

	if req.GiveNum != 0 {
		query = query.Where("give_num = ?", req.GiveNum)
	}

	if req.IsGive != 0 {
		query = query.Where("is_give = ?", req.IsGive)
	}

	if req.IsDelete != 0 {
		query = query.Where("is_delete = ?", req.IsDelete)
	}

	if !req.BeginAt.IsZero() {
		query = query.Where("give_at >= ?", req.BeginAt)
	}

	if !req.EndAt.IsZero() {
		query = query.Where("give_at <= ?", req.EndAt)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		d.log.Error("查询VIP赠送记录总数失败: %v", err)
		return nil, err
	}

	// 获取列表
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("create_at DESC").Find(&gives).Error; err != nil {
		d.log.Error("查询VIP赠送记录列表失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.UserVipGiveDto
	for _, give := range gives {
		dtoList = append(dtoList, d.convertVipGiveModel2Dto(give))
	}

	return &dto.VipGivePageResponse{
		Total: total,
		List:  dtoList,
	}, nil
}

func (d *userVipRepo) getDB(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

func (d *userVipRepo) ListTodayExpireVIP(today time.Time, tx ...*gorm.DB) ([]*dto.UserVipDto, error) {
	var vipInfo []*dto.UserVipDto

	if err := d.getDB(tx).Model(&model.UserVip{}).
		Where("is_delete = ?", model.StatusDisabled).
		Where("expire_at < ?", today).
		Find(&vipInfo).Error; err != nil {
		logger.Log().Error("获取今天过期的VIP失败, err: %v", err)
		return nil, err
	}
	return vipInfo, nil
}

func (d *userVipRepo) BatchUpdateUserVipIsExpireAndIsDelete(userIds []int64, tx ...*gorm.DB) error {
	if err := d.getDB(tx).Model(&model.UserVip{}).Where("user_id IN (?)", userIds).Updates(map[string]any{
		"is_expire": model.IsExpire,
		"is_delete": model.StatusEnabled,
	}).Error; err != nil {
		d.log.Error("批量更新用户VIP过期状态错误: %v", err.Error())
		return err
	}
	return nil
}

func (d *userVipRepo) ListTodayGiveVIP(today time.Time, tx ...*gorm.DB) ([]*dto.UserVipGiveDto, error) {
	var gives []*dto.UserVipGiveDto
	endOfDay := today.Add(24 * time.Hour) // 计算明日 00:00:00
	if err := d.getDB(tx).Model(&model.UserVipGive{}).
		Where("is_delete = ?", model.StatusDisabled).
		Where("is_give = ?", model.GiveStatusPending).
		Where("give_at >= ?", today).
		Where("give_at < ?", endOfDay).
		Find(&gives).Error; err != nil {
		logger.Log().Error("获取今天需要赠送的VIP失败, err: %v", err)
		return nil, err
	}
	return gives, nil
}

// ListCurrentHourGiveVIP 获取当前小时内需要赠送钻石的VIP记录（测试模式）
func (d *userVipRepo) ListCurrentHourGiveVIP(currentTime time.Time, tx ...*gorm.DB) ([]*dto.UserVipGiveDto, error) {
	var gives []*dto.UserVipGiveDto
	afterTenMinutes := currentTime.Add(10 * time.Minute) // 十分钟间隔

	if err := d.getDB(tx).Model(&model.UserVipGive{}).
		Where("is_delete = ?", model.StatusDisabled).
		Where("is_give = ?", model.GiveStatusPending).
		//Where("give_at >= ?", currentTime).
		Where("give_at < ?", afterTenMinutes).
		Find(&gives).Error; err != nil {
		logger.Log().Error("获取当前十分钟内需要赠送的VIP失败, err: %v", err)
		return nil, err
	}
	return gives, nil
}

// BatchCreateVipGive 批量创建VIP赠送记录
func (d *userVipRepo) BatchCreateVipGive(giveList []*dto.UserVipGiveDto, tx ...*gorm.DB) error {
	if len(giveList) == 0 {
		return nil
	}

	now := time.Now()
	var gives []*model.UserVipGive

	// 转换DTO为模型
	for _, giveDto := range giveList {
		// 如果没有指定赠送时间，使用当前时间
		giveAt := giveDto.GiveAt
		if giveAt.IsZero() {
			giveAt = now
		}

		give := &model.UserVipGive{
			UserId:   giveDto.UserId,
			GoodsId:  giveDto.GoodsId,
			OrderId:  giveDto.OrderId,
			VipType:  giveDto.VipType,
			GiveAt:   giveAt,
			GiveNum:  giveDto.GiveNum,
			IsGive:   model.GiveStatusPending, // 未赠送
			IsDelete: int8(model.StatusDisabled),
			CreateAt: now,
		}
		gives = append(gives, give)
	}

	// 批量插入
	if err := d.getDb(tx).Model(&model.UserVipGive{}).CreateInBatches(gives, 100).Error; err != nil {
		d.log.Error("批量创建VIP赠送记录错误: %v", err.Error())
		return err
	}

	d.log.Info("成功批量创建 %d 条VIP赠送记录", len(gives))
	return nil
}

// GetLastVipGiveByUserId 获取用户最后一条赠送记录
func (d *userVipRepo) GetLastVipGiveByUserId(userId int64, tx ...*gorm.DB) (*dto.UserVipGiveDto, error) {
	var give model.UserVipGive
	if err := d.getDb(tx).Model(&model.UserVipGive{}).Where(map[string]any{
		"user_id":   userId,
		"is_delete": model.StatusDisabled,
	}).Order("give_at DESC").First(&give).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询用户最后一条赠送记录错误: %v", err.Error())
		return nil, err
	}
	return d.convertVipGiveModel2Dto(&give), nil
}

func (d *userVipRepo) BatchUpdateVipGiveStatusAndIsDelete(giveList []*dto.UserVipGiveDto, tx ...*gorm.DB) error {
	if len(giveList) == 0 {
		return nil
	}

	// 提取ID列表
	ids := make([]int64, 0, len(giveList))
	for _, give := range giveList {
		ids = append(ids, give.ID)
	}

	// 批量更新
	if err := d.getDb(tx).Model(&model.UserVipGive{}).Where("id IN (?)", ids).Updates(map[string]any{
		"is_give":   model.GiveStatusDone,
		"is_delete": model.StatusEnabled,
	}).Error; err != nil {
		d.log.Error("批量更新VIP赠送记录状态错误: %v", err.Error())
		return err
	}

	return nil
}

func (d *userVipRepo) BatchDeleteVipGiveById(vipGiveIds []int64, tx ...*gorm.DB) error {
	if len(vipGiveIds) == 0 {
		return nil
	}

	if err := d.getDb(tx).Model(&model.UserVipGive{}).Where("id IN (?)", vipGiveIds).Updates(map[string]any{
		"is_delete": model.StatusEnabled,
	}).Error; err != nil {
		d.log.Error("批量删除VIP赠送记录错误: %v", err.Error())
		return err
	}

	return nil
}

// GetLatestVipGivesByUserId 获取用户最新创建的N条赠送记录
func (d *userVipRepo) GetLatestVipGivesByUserId(userId int64, limit int, tx ...*gorm.DB) ([]*dto.UserVipGiveDto, error) {
	var gives []*model.UserVipGive
	if err := d.getDb(tx).Model(&model.UserVipGive{}).
		Where("user_id = ? AND is_delete = ? AND is_give = ?", userId, model.StatusDisabled, model.StatusDisabled).
		Order("give_at DESC").
		Limit(limit).
		Find(&gives).Error; err != nil {
		d.log.Error("查询用户最新赠送记录失败: %v", err)
		return nil, err
	}

	var dtoList []*dto.UserVipGiveDto
	for _, give := range gives {
		dtoList = append(dtoList, d.convertVipGiveModel2Dto(give))
	}

	return dtoList, nil
}

func (d *userVipRepo) UpdateUserVipById(vipId int64, fields map[string]any, tx ...*gorm.DB) error {
	if len(fields) == 0 {
		return nil
	}

	if err := d.getDb(tx).Model(&model.UserVip{}).Where("id = ?", vipId).Updates(fields).Error; err != nil {
		d.log.Error("更新用户VIP信息错误: %v", err.Error())
		return err
	}
	return nil
}
