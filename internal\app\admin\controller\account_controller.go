package controller

import (
	"chongli/component"
	"chongli/internal/app/admin/dto"
	"chongli/internal/dao"
	"chongli/internal/model"
	"chongli/pkg/logger"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AccountController struct {
	log          *logger.Logger
	adminUserDao *dao.AdminUserDao
}

func NewAccountController(bootStrap *component.BootStrap) *AccountController {
	return &AccountController{
		log:          bootStrap.Log,
		adminUserDao: dao.NewAdminUserDao(bootStrap),
	}
}

// AccountList 获取管理员账户列表
func (c *AccountController) AccountList(ctx *gin.Context) {
	var req dto.AdminUserListRequestDto
	if err := ctx.ShouldBindQuery(&req); err != nil {
		ctx.JSON(200, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	condition := map[string]interface{}{
		// "is_delete": model.StatusDisabled, // 只查询未删除的用户
	}

	// 邮箱筛选
	if req.Email != "" {
		condition["email"] = req.Email
	}

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 查询用户列表
	users, total, err := c.adminUserDao.GetAdminUserList(condition, offset, req.PageSize)
	if err != nil {
		c.log.Error("获取管理员用户列表失败: %v", err)
		ctx.JSON(200, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	// 转换为DTO
	userDtos := make([]*dto.AdminUserDto, 0, len(users))
	for _, user := range users {
		userDto := c.adminUserDao.ConvertToAdminUserDto(user)
		userDtos = append(userDtos, userDto)
	}

	// 返回结果
	ctx.JSON(200, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": dto.AdminUserListResponseDto{
			List:     userDtos,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		},
	})
}

// AccountAdd 添加或修改管理员账户
func (c *AccountController) AccountAdd(ctx *gin.Context) {
	var req dto.AdminUserCreateRequestDto
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(200, gin.H{
			"code":    400,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前操作用户ID
	currentUserID := ctx.GetInt64("admin_user_id")

	// 判断是新增还是修改
	if req.ID != nil && *req.ID > 0 {
		// 修改用户
		c.updateUser(ctx, &req, currentUserID)
	} else {
		// 新增用户
		c.createUser(ctx, &req, currentUserID)
	}
}

// createUser 创建新用户
func (c *AccountController) createUser(ctx *gin.Context, req *dto.AdminUserCreateRequestDto, currentUserID int64) {
	// 检查邮箱是否已存在
	existingUser, err := c.adminUserDao.GetAdminUserByCondition(map[string]interface{}{
		"email": req.Email,
	})
	if err != nil {
		c.log.Error("检查邮箱是否存在失败: %v", err)
		ctx.JSON(200, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if existingUser != nil && existingUser.IsDelete == model.StatusDisabled {
		ctx.JSON(200, gin.H{
			"code":    400,
			"message": "邮箱已存在",
		})
		return
	}

	// 创建新用户
	newUser := &model.AdminUser{
		Email:     req.Email,
		Nickname:  req.Nickname,
		Avatar:    req.Avatar,
		CreatedBy: currentUserID,
		UpdatedBy: currentUserID,
		IsDelete:  model.StatusFlag(req.IsDelete), // 未删除
	}

	err = c.adminUserDao.CreateAdminUser(newUser)
	if err != nil {
		c.log.Error("创建管理员用户失败: %v", err)
		ctx.JSON(200, gin.H{
			"code":    500,
			"message": "创建失败",
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code":    200,
		"message": "创建成功",
		"data":    gin.H{"id": newUser.ID},
	})
}

// updateUser 修改用户
func (c *AccountController) updateUser(ctx *gin.Context, req *dto.AdminUserCreateRequestDto, currentUserID int64) {
	userID := *req.ID

	// 不能修改自己
	if userID == currentUserID {
		ctx.JSON(200, gin.H{
			"code":    400,
			"message": "不能修改自己",
		})
		return
	}

	// 检查用户是否存在
	existingUser, err := c.adminUserDao.GetAdminUserByID(userID)
	if err != nil {
		c.log.Error("获取用户信息失败: %v", err)
		ctx.JSON(200, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if existingUser == nil {
		ctx.JSON(200, gin.H{
			"code":    404,
			"message": "用户不存在",
		})
		return
	}

	// 检查邮箱是否与其他用户冲突
	if existingUser.Email != req.Email {
		conflictUser, err := c.adminUserDao.GetAdminUserByCondition(map[string]interface{}{
			"email": req.Email,
		})
		if err != nil {
			c.log.Error("检查邮箱冲突失败: %v", err)
			ctx.JSON(200, gin.H{
				"code":    500,
				"message": "系统错误",
			})
			return
		}

		if conflictUser != nil && conflictUser.ID != userID {
			ctx.JSON(200, gin.H{
				"code":    400,
				"message": "邮箱已被其他用户使用",
			})
			return
		}
	}

	// 更新用户信息
	updates := map[string]interface{}{
		"email":      req.Email,
		"nickname":   req.Nickname,
		"avatar":     req.Avatar,
		"updated_by": currentUserID,
		"is_delete":  model.StatusFlag(req.IsDelete),
	}

	err = c.adminUserDao.UpdateAdminUserByCondition(
		map[string]interface{}{"id": userID},
		updates,
	)
	if err != nil {
		c.log.Error("更新用户信息失败: %v", err)
		ctx.JSON(200, gin.H{
			"code":    500,
			"message": "更新失败",
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    gin.H{"id": userID},
	})
}

// AccountDelete 删除管理员账户（软删除）
func (c *AccountController) AccountDelete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		ctx.JSON(200, gin.H{
			"code":    400,
			"message": "无效的用户ID",
		})
		return
	}

	// 获取当前操作用户ID
	currentUserID := ctx.GetInt64("admin_user_id")

	// 不能删除自己
	if id == currentUserID {
		ctx.JSON(200, gin.H{
			"code":    400,
			"message": "不能删除自己",
		})
		return
	}

	// 检查用户是否存在
	user, err := c.adminUserDao.GetAdminUserByID(id)
	if err != nil {
		c.log.Error("获取用户信息失败: %v", err)
		ctx.JSON(200, gin.H{
			"code":    500,
			"message": "系统错误",
		})
		return
	}

	if user == nil || user.IsDelete == model.StatusEnabled {
		ctx.JSON(200, gin.H{
			"code":    404,
			"message": "用户不存在",
		})
		return
	}

	// 执行软删除
	err = c.adminUserDao.DeleteAdminUser(id)
	if err != nil {
		c.log.Error("删除用户失败: %v", err)
		ctx.JSON(200, gin.H{
			"code":    500,
			"message": "删除失败",
		})
		return
	}

	ctx.JSON(200, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}
