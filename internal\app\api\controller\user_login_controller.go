package controller

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/internal/app/api/biz"
	"chongli/internal/service"
	"chongli/internal/service/constant"
	"chongli/internal/service/dto"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"crypto/md5"
	"fmt"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type UserLoginController struct {
	apolloConfig *apollo.Config
	loginService *biz.UserLoginService
	userService  *service.UserService
	log          *logger.Logger
}

func NewUserLoginController(
	bootstrap *component.BootStrap,
	loginService *biz.UserLoginService,
	userService *service.UserService,
) *UserLoginController {
	return &UserLoginController{
		apolloConfig: bootstrap.Config,
		log:          bootstrap.Log,
		loginService: loginService,
		userService:  userService,
	}
}

func (c *UserLoginController) getRequestHeader(ctx *gin.Context, req *dto.UserLoginRequest) {
	req.Version = ctx.Request.Header.Get("version")
	req.Channel = ctx.Request.Header.Get("channel")
	req.DeviceId = ctx.Request.Header.Get("deviceId")
	req.Ip = ctx.ClientIP()
}

func (c *UserLoginController) login(ctx *gin.Context, req *dto.UserLoginRequest) {
	// 获取请求头信息
	c.getRequestHeader(ctx, req)
	if req.DeviceId == "" || req.Channel == "" {
		response.Response(ctx, req, nil, errpkg.NewMiddleError(response.HeaderError), response.WithSLSLog)
		return
	}
	// 校验登录请求类型
	if req.Type != 1 && req.Type != 2 && req.Type != 3 && req.Type != 4 {
		response.Response(ctx, req, nil, errpkg.NewMiddleError(response.LoginTypeTypeError), response.WithSLSLog)
		return
	}
	// 校验手机号格式
	if req.Type == 2 && req.Phone != "" {
		if !utils.IsValidPhoneNumber(req.Phone) {
			response.Response(ctx, req, nil, errpkg.NewLowError(response.PhoneFormatError), response.WithSLSLog)
			return
		}
	}
	// 根据登录请求类型处理业务逻辑
	var (
		resp *dto.UserLoginResponse
		err  errpkg.IError
	)
	switch req.Type {
	case 1:
		resp, err = c.loginService.OneClickLogin(req)
	case 2:
		resp, err = c.loginService.PhoneCodeLogin(req)
	case 3:
		resp, err = c.loginService.AppleLogin(req)
	}
	response.Response(ctx, nil, resp, err, response.WithSLSLog)
}

// OneClickLogin 一键登录
func (c *UserLoginController) OneClickLogin(ctx *gin.Context) {
	var req dto.UserOneClickLoginRequest
	if errBind := ctx.ShouldBindJSON(&req); errBind != nil {
		response.Response(ctx, req, nil, errpkg.NewMiddleError(response.BadRequest), response.WithSLSLog)
		return
	}
	c.login(ctx, &dto.UserLoginRequest{
		UserOneClickLoginRequest: dto.UserOneClickLoginRequest{
			Gyuid: req.Gyuid,
			Token: req.Token,
		},
		Type: constant.OneClickLogin,
	})
}

// SendCode 发送验证码
func (c *UserLoginController) SendCode(ctx *gin.Context) {
	var req dto.UserSendCodeRequest
	if errBind := ctx.ShouldBindJSON(&req); errBind != nil {
		response.Response(ctx, req, nil, errpkg.NewMiddleError(response.BadRequest), response.WithSLSLog)
		return
	}
	// 校验手机号格式
	if !utils.IsValidPhoneNumber(req.Phone) {
		response.Response(ctx, req, nil, errpkg.NewLowError(response.PhoneFormatError), response.WithSLSLog)
		return
	}
	if c.apolloConfig.Env == "prod" {
		// 判断时间戳
		if req.Timestamp+constant.ValidTimestamp < time.Now().Unix() {
			response.Response(ctx, req, nil, errpkg.NewLowError(response.TimestampError), response.WithSLSLog)
			return
		}
		// 判断signature是否一致
		sig := c.smsMD5(req.Phone, strconv.Itoa(int(req.Timestamp)))
		if sig != req.Signature {
			logger.Log().Error("签名不一致: %v 与 %v", sig, req.Signature)
			response.Response(ctx, req, nil, errpkg.NewLowError(response.SignatureError), response.WithSLSLog)
			return
		}
	}
	// 处理业务逻辑
	data, err := c.loginService.SendCode(&dto.UserSendCodeRequest{
		Phone:     req.Phone,
		Signature: req.Signature,
		Timestamp: req.Timestamp,
	})
	// 响应
	response.Response(ctx, req, data, err, response.WithSLSLog)
}

// 发送验证码之前的MD5加密获取签名
func (c *UserLoginController) smsMD5(phone, timestamp string) string {
	return fmt.Sprintf("%x",
		md5.Sum([]byte("phone="+phone+"&"+"secret_key="+c.apolloConfig.PhoneCodeSecret+"&"+"timestamp="+timestamp)))
}

// PhoneCodeLogin 手机验证码登录
func (c *UserLoginController) PhoneCodeLogin(ctx *gin.Context) {
	var req dto.UserPhoneCodeLoginRequest
	if errBind := ctx.ShouldBindJSON(&req); errBind != nil {
		response.Response(ctx, req, nil, errpkg.NewMiddleError(response.BadRequest), response.WithSLSLog)
		return
	}
	c.login(ctx, &dto.UserLoginRequest{
		UserPhoneCodeLoginRequest: dto.UserPhoneCodeLoginRequest{
			Code:  req.Code,
			Phone: req.Phone,
		},
		Type: constant.PhoneCodeLogin,
	})
}

// AppleLogin 苹果登录
func (c *UserLoginController) AppleLogin(ctx *gin.Context) {
	c.login(ctx, &dto.UserLoginRequest{
		Type: constant.AppleLogin,
	})
}
