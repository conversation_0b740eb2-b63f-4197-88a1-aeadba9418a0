package service

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"
	"encoding/json"
	"errors"

	"gorm.io/gorm"
)

type TemplateService struct {
	templateRepo         repo.TemplateAIRepo
	log                  *logger.Logger
	templateCategoryRepo repo.TemplateCategoryRepo
}

func NewTemplateService(
	templateRepo repo.TemplateAIRepo,
	templateCategoryRepo repo.TemplateCategoryRepo,
	component *component.BootStrap) *TemplateService {
	return &TemplateService{
		templateRepo:         templateRepo,
		log:                  component.Log,
		templateCategoryRepo: templateCategoryRepo,
	}
}

// GetAiTemplateById 根据ID获取AI模板，返回解析好的模板
func (s *TemplateService) GetAiTemplateById(ctx context.Context, templateId uint64, tx *gorm.DB) (*dto.TemplateAIDTO, *dto.AIPicTemplate, error) {
	templateInfo, err := s.templateRepo.GetByID(ctx, templateId, tx)
	if err != nil {
		return nil, nil, err
	}
	if templateInfo == nil || templateInfo.ID <= 0 {
		return nil, nil, errors.New("模板不存在")
	}
	if templateInfo.Status != 1 {
		return nil, nil, errors.New("模板已下架")
	}

	// 验证模板分类状态
	if templateInfo.CategoryID > 0 {
		// 检查分类是否存在
		if templateInfo.Category.ID <= 0 {
			return nil, nil, errors.New("模板分类不存在")
		}
		// 检查分类是否已删除
		if templateInfo.Category.IsDelete == 1 {
			return nil, nil, errors.New("模板分类已删除")
		}
		// 检查分类是否已禁用
		if templateInfo.Category.IsActive == -1 {
			return nil, nil, errors.New("模板分类已禁用")
		}
	}

	templateParam := dto.AIPicTemplate{}
	// 将JSONMap转换为[]byte
	jsonBytes, err := json.Marshal(templateInfo.VariablesJSON)
	if err != nil {
		s.log.Error("模板参数序列化失败: %v", err)
		return nil, nil, errors.New("模板参数序列化失败")
	}
	// 解析JSON到结构体
	err = json.Unmarshal(jsonBytes, &templateParam)
	if err != nil {
		s.log.Error("模板参数解析失败: %v", err)
		return nil, nil, errors.New("模板参数解析失败")
	}
	return templateInfo, &templateParam, nil
}
