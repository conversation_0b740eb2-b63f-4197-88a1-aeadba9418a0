package dto

import "time"

type UserDiamondRecordDto struct {
	Id         int64     `json:"id"`          // 主键
	UserId     int64     `json:"user_id"`     // 用户id
	OrderId    string    `json:"order_id"`    // 订单id
	GoodsId    int64     `json:"goods_id"`    // 商品id
	TemplateId int64     `json:"template_id"` // 制作的模板id
	Diamond    uint64    `json:"diamond"`     // 钻石变化数额
	Balance    uint64    `json:"balance"`     // 用户钻石余额
	Type       int       `json:"type"`        // 类型：1增加，-1减少
	Mark       string    `json:"mark"`        // 备注
	Version    string    `json:"version"`     // 版本
	Channel    string    `json:"channel"`     // 客户端/渠道
	CreateAt   time.Time `json:"create_at"`   // 创建时间
	IsDelete   int       `json:"is_delete"`   // 删除标记：-1未删除，1删除
}

type UserDiamondRecordPageRequest struct {
	Page       int       `json:"page" form:"page" binding:"min=1"`
	PageSize   int       `json:"page_size" form:"page_size" binding:"min=1"`
	UserId     int64     `json:"user_id" form:"user_id"`
	OrderId    string    `json:"order_id" form:"order_id"`
	GoodsId    int64     `json:"goods_id" form:"goods_id"`
	TemplateId int64     `json:"template_id" form:"template_id"`
	Type       int       `json:"type" form:"type"`
	Channel    string    `json:"channel" form:"channel"`
	Version    string    `json:"version" form:"version"`
	BeginAt    time.Time `json:"begin_at" form:"begin_at"`
	EndAt      time.Time `json:"end_at" form:"end_at"`
}

type UserDiamondRecordPageResponse struct {
	Total int64                   `json:"total"`
	List  []*UserDiamondRecordDto `json:"list"`
}

type AddAndSubUserDiamondRequest struct {
	UserId  int64  `json:"user_id" binding:"required"`
	Diamond uint64 `json:"diamond" binding:"required"`
	Mark    string `json:"mark" binding:"required"`
	Version string `json:"version"`
	Channel string `json:"channel"`
}
