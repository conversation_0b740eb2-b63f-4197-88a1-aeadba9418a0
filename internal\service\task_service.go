package service

import (
	"context"
	"fmt"
	"time"

	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"

	"gorm.io/gorm"
)

type TaskService struct {
	db driver.ITransaction
	// taskRepo        repo.TaskRepo
	taskStepRepo    repo.TaskStepRepo
	userWorkRepo    repo.UserWorkRepo
	userService     *UserService
	userWorkService *UserWorkService
	log             *logger.Logger
}

func NewTaskService(
	component *component.BootStrap,
	// taskRepo repo.TaskRepo,
	taskStepRepo repo.TaskStepRepo,
	userWorkRepo repo.UserWorkRepo,
	userService *UserService,
	userWorkService *UserWorkService,
) *TaskService {
	return &TaskService{
		db: component.Tx,
		// taskRepo:        taskRepo,
		taskStepRepo:    taskStepRepo,
		userWorkRepo:    userWorkRepo,
		userService:     userService,
		userWorkService: userWorkService,
		log:             component.Log,
	}
}

// CreateSteps 创建任务和步骤
func (s *TaskService) CreateSteps(ctx context.Context, req []*dto.TaskStepDTO, tx ...*gorm.DB) error {

	// 转换为 DTO

	if err := s.taskStepRepo.CreateBatch(ctx, req, tx...); err != nil {
		return fmt.Errorf("创建任务步骤失败: %w", err)
	}
	return nil
}

// StepDone 完成某个步骤，如果任务失败则设置为失败
func (s *TaskService) StepDone(ctx context.Context, step *dto.TaskStepDTO, tx ...*gorm.DB) error {
	// 更新步骤状态
	if err := s.updateStepStatus(ctx, step, tx...); err != nil {
		return fmt.Errorf("更新步骤状态失败: %w", err)
	}
	return nil
}

// StepFail 将步骤设置为失败
func (s *TaskService) StepFail(ctx context.Context, step *dto.TaskStepDTO, workError error, tx ...*gorm.DB) (err error) {
	// 检查是否需要创建事务
	var dbTx *gorm.DB

	if len(tx) > 0 && tx[0] != nil {
		// 使用传入的事务
		dbTx = tx[0]
	} else {
		// 创建新事务
		dbTx = s.db.MysqlDbTxBegin()
		if dbTx.Error != nil {
			return fmt.Errorf("stepID %d 创建事务失败: %w", step.ID, dbTx.Error)
		}

		// 设置延迟执行的事务处理
		defer func() {
			if err != nil {
				// 发生错误时回滚
				if rollbackErr := dbTx.Rollback().Error; rollbackErr != nil {
					s.log.Error("stepID %d 事务回滚失败: %v", step.ID, rollbackErr)
				}
			} else {
				// 成功时提交
				if commitErr := dbTx.Commit().Error; commitErr != nil {
					err = fmt.Errorf("stepID %d 事务提交失败: %w", step.ID, commitErr)
				}
			}
		}()
	}

	// 更新步骤到数据库
	updates := map[string]any{
		"status":      model.StepStatusFailed,
		"error_msg":   step.ErrorMsg,
		"finished_at": time.Now(),
	}
	err = s.taskStepRepo.Update(ctx, step.ID, updates, dbTx)
	if err != nil {
		return fmt.Errorf("stepID %d 更新步骤状态失败: %w", step.ID, err)
	}
	if workError != nil {
		step.ErrorMsg = workError.Error()
	}
	// 更新用户作品状态
	err = s.userWorkService.UpdateUserWorkOnFailure(ctx, step.WorkID, step.ErrorMsg, dbTx)
	if err != nil {
		return fmt.Errorf("stepID %d 更新用户作品状态失败: %w", step.ID, err)
	}

	return nil
}

// getStepByID 根据步骤ID获取步骤信息
func (s *TaskService) getStepByID(ctx context.Context, stepID int64, tx ...*gorm.DB) (*dto.TaskStepDTO, error) {
	query := &dto.GetStepQuery{
		Step: &dto.TaskStepDTO{
			ID: stepID,
		},
	}

	stepDTOs, err := s.taskStepRepo.GetStepQuery(ctx, query, tx...)
	if err != nil {
		return nil, fmt.Errorf("获取步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return nil, fmt.Errorf("步骤不存在")
	}

	return stepDTOs[0], nil
}

// updateStepStatus 更新步骤状态
func (s *TaskService) updateStepStatus(ctx context.Context, stepDTO *dto.TaskStepDTO, tx ...*gorm.DB) error {
	// 更新步骤状态
	now := time.Now()
	stepDTO.FinishedAt = &now

	// 更新步骤到数据库
	updates := map[string]any{
		"status":      stepDTO.Status,
		"error_msg":   stepDTO.ErrorMsg,
		"finished_at": time.Now(),
	}

	return s.taskStepRepo.Update(ctx, stepDTO.ID, updates, tx...)
}

// CompleteTask 完成任务
// func (s *TaskService) CompleteTask(ctx context.Context, task *dto.TaskDTO, errorMsg string, tx ...*gorm.DB) error {

// 	// 更新任务状态
// 	if errorMsg != "" {
// 		task.Status = "failed"
// 		task.ErrorMsg = errorMsg
// 	} else {
// 		task.Status = "done"
// 	}

// 	return s.taskRepo.Update(ctx, task.ID, map[string]any{
// 		"status":    task.Status,
// 		"error_msg": task.ErrorMsg,
// 	}, tx...)
// }

// GetPendingTaskStep 根据任务类型获取一个待执行的任务步骤，获取后将步骤状态更改为运行中
func (s *TaskService) GetPendingTaskStep(ctx context.Context, stepType string, tx ...*gorm.DB) (*dto.TaskStepDTO, error) {
	// 构建查询条件：获取指定任务类型的第一个待执行步骤
	limit := 1
	query := &dto.GetStepQuery{
		Step: &dto.TaskStepDTO{
			Status:   model.StepStatusPending,
			StepName: stepType,
			UserWork: &dto.UserWorks{
				Status: 0,
			},
		},
		Limit: &limit,
	}

	// 通过 DAO 层查找步骤
	stepDTOs, err := s.taskStepRepo.GetStepQuery(ctx, query, tx...)
	if err != nil {
		return nil, fmt.Errorf("获取待执行步骤失败: %w", err)
	}

	if len(stepDTOs) == 0 {
		return nil, nil // 没有待执行步骤时返回 nil
	}
	return stepDTOs[0], nil
}

// GetStepQuery 根据查询条件获取步骤信息，可选外部传入事务
func (s *TaskService) GetStepQuery(ctx context.Context, query *dto.GetStepQuery, tx ...*gorm.DB) ([]*dto.TaskStepDTO, error) {
	return s.taskStepRepo.GetStepQuery(ctx, query, tx...)
}

// UpdateStepStatusAndResult 更新步骤状态和结果
func (s *TaskService) UpdateStepStatusAndResult(ctx context.Context, step *dto.TaskStepDTO, status string, result []byte, tx ...*gorm.DB) error {
	now := time.Now()
	updates := map[string]any{
		"status":     status,
		"result":     string(result),
		"updated_at": now,
	}

	// 如果状态是 waiting_result，设置开始时间；如果是完成状态，设置完成时间
	switch status {
	case model.StepStatusWaitingResult:
		updates["started_at"] = now
	case model.StepStatusDone, model.StepStatusFailed:
		updates["finished_at"] = now
	}

	err := s.taskStepRepo.Update(ctx, step.ID, updates, tx...)
	if err != nil {
		return fmt.Errorf("更新任务步骤失败: %w", err)
	}

	return nil
}
