package server

import (
	"chongli/component"
	"chongli/internal/app/task/registry"

	"github.com/robfig/cron/v3"
)

// TimerTask 定时任务结构体
type TimerTask struct {
	C    *cron.Cron
	Task *Task
}

// Task 任务结构体
type Task struct {
	UserTask     *registry.UserTask
	PayOrderTask *registry.PayOrderTask
	PicTask      *registry.PicTask
}

// TaskServer 任务服务器
type TaskServer struct {
	timerTask *TimerTask
	bootstrap *component.BootStrap
}

// NewTaskServer 创建任务服务器
func NewTaskServer(bootstrap *component.BootStrap) *TaskServer {
	c := cron.New(cron.WithSeconds(), cron.WithChain(cron.SkipIfStillRunning(cron.DiscardLogger)))
	timerTask := initTask(c, bootstrap)
	return &TaskServer{
		timerTask: timerTask,
		bootstrap: bootstrap,
	}
}

// Start 启动任务服务器
func (s *TaskServer) Start() {
	s.bootstrap.Log.Info("Starting Task server")

	s.timerTask.C.Start()

	// 阻塞
	select {}
}
