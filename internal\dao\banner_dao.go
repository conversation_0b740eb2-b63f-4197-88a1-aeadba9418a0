package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"chongli/pkg/utils"
	"errors"
	"gorm.io/gorm"
	"time"
)

type bannerRepo struct {
	log *logger.Logger
	db  *gorm.DB
}

func NewBannerRepo(bootStrap *component.BootStrap) repo.BannerRepo {
	return &bannerRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
	}
}

func (d *bannerRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// convertModel2Dto 转换模型为DTO
func (d *bannerRepo) convertModel2Dto(banner *model.Banner) *dto.BannerDto {
	return &dto.BannerDto{
		ID:            banner.ID,
		Title:         banner.Title,
		Image:         banner.Image,
		Video:         banner.Video,
		Desc:          banner.Desc,
		Sort:          banner.Sort,
		IsActive:      banner.IsActive,
		IsDelete:      banner.IsDelete,
		CreateAt:      banner.CreateAt,
		UpdateAt:      banner.UpdateAt,
		MaxVersion:    banner.MaxVersion,
		MaxVersionInt: banner.MaxVersionInt,
		TemplateId:    banner.TemplateId,
		CategoryId:    banner.CategoryId,
		CategoryName:  banner.CategoryName,
		Location:      banner.Location,
	}
}

// CreateBanner 创建Banner
func (d *bannerRepo) CreateBanner(req *dto.CreateBannerRequest, tx ...*gorm.DB) (*dto.BannerDto, error) {
	now := time.Now()

	banner := &model.Banner{
		Title:         req.Title,
		Image:         req.Image,
		Video:         req.Video,
		Desc:          req.Desc,
		Sort:          req.Sort,
		MaxVersion:    req.MaxVersion,
		MaxVersionInt: utils.VersionToVersionInt(req.MaxVersion),
		IsActive:      int8(model.StatusEnabled),
		IsDelete:      int8(model.StatusDisabled),
		CreateAt:      now,
		UpdateAt:      now,
		TemplateId:    req.TemplateId,
		CategoryId:    req.CategoryId,
		CategoryName:  req.CategoryName,
		Location:      req.Location,
	}

	if err := d.getDb(tx).Model(&model.Banner{}).Create(banner).Error; err != nil {
		d.log.Error("创建Banner错误: %v", err.Error())
		return nil, err
	}

	return d.convertModel2Dto(banner), nil
}

// GetBannerById 根据ID查询Banner
func (d *bannerRepo) GetBannerById(id int64, tx ...*gorm.DB) (*dto.BannerDto, error) {
	var banner model.Banner
	if err := d.getDb(tx).Model(&model.Banner{}).Where("id = ?", id).First(&banner).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("查询Banner错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&banner), nil
}

// UpdateBanner 更新Banner
func (d *bannerRepo) UpdateBanner(req *dto.UpdateBannerRequest, tx ...*gorm.DB) error {
	if req.ID == 0 {
		return errors.New("banner ID不能为空")
	}

	var updates = make(map[string]any)

	if req.Title != "" {
		updates["title"] = req.Title
	}

	if req.Image != "" {
		updates["image"] = req.Image
	}

	if req.Video != "" {
		updates["video"] = req.Video
	}

	if req.Desc != "" {
		updates["desc"] = req.Desc
	}

	if req.Sort != 0 {
		updates["sort"] = req.Sort
	}

	if req.MaxVersion != "" {
		updates["max_version"] = req.MaxVersion
		updates["max_version_int"] = utils.VersionToVersionInt(req.MaxVersion)
	}

	if req.Location != "" {
		updates["location"] = req.Location
	}

	if req.IsActive != 0 {
		updates["is_active"] = req.IsActive
	}

	if req.IsDelete != 0 {
		updates["is_delete"] = req.IsDelete
	}

	// 支持清空跳转模板或者跳转分类

	if req.TemplateId >= 0 {
		updates["template_id"] = req.TemplateId
	}

	if req.CategoryId >= 0 && len(req.CategoryName) >= 0 {
		updates["category_id"] = req.CategoryId
		updates["category_name"] = req.CategoryName
	}

	if err := d.getDb(tx).Model(&model.Banner{}).Where("id = ?", req.ID).Updates(updates).Error; err != nil {
		d.log.Error("更新Banner错误: %v", err.Error())
		return err
	}
	return nil
}

// DeleteBanner 删除Banner（软删除）
func (d *bannerRepo) DeleteBanner(id int64, tx ...*gorm.DB) error {
	updates := map[string]any{
		"is_delete": model.StatusEnabled,
	}

	if err := d.getDb(tx).Model(&model.Banner{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		d.log.Error("删除Banner错误: %v", err.Error())
		return err
	}
	return nil
}

// GetBannerByTitle 根据标题查询Banner
func (d *bannerRepo) GetBannerByTitle(title string, tx ...*gorm.DB) (*dto.BannerDto, error) {
	var banner model.Banner
	if err := d.getDb(tx).Model(&model.Banner{}).Where(map[string]any{
		"title":     title,
		"is_delete": model.StatusDisabled,
	}).First(&banner).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据标题查询Banner错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&banner), nil
}

// PageBanner 分页查询Banner
func (d *bannerRepo) PageBanner(req *dto.BannerPageRequest) (*dto.BannerPageResponse, error) {
	var banners []*model.Banner
	var total int64

	query := d.db.Model(&model.Banner{})

	// 添加查询条件
	if req.ID != 0 {
		query = query.Where("id = ?", req.ID)
	}

	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}

	if req.IsActive != 0 {
		query = query.Where("is_active = ?", req.IsActive)
	}

	if req.IsDelete != 0 {
		query = query.Where("is_delete = ?", req.IsDelete)
	}

	if req.TemplateId != 0 {
		query = query.Where("template_id = ?", req.TemplateId)
	}

	if req.CategoryId != 0 {
		query = query.Where("category_id = ?", req.CategoryId)
	}

	if req.MaxVersion != "" {
		query = query.Where("max_version = ?", req.MaxVersion)
	}

	if req.Location != "" {
		query = query.Where("location = ?", req.Location)
	}

	if !req.BeginAt.IsZero() {
		query = query.Where("create_at >= ?", req.BeginAt)
	}

	if !req.EndAt.IsZero() {
		query = query.Where("create_at <= ?", req.EndAt)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		d.log.Error("查询Banner总数失败: %v", err)
		return nil, err
	}

	// 获取列表
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("sort ASC, create_at DESC").Find(&banners).Error; err != nil {
		d.log.Error("查询Banner列表失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.BannerDto
	for _, banner := range banners {
		dtoList = append(dtoList, d.convertModel2Dto(banner))
	}

	return &dto.BannerPageResponse{
		Total: total,
		List:  dtoList,
	}, nil
}

// ListBanner 列表查询Banner（前端API用）
func (d *bannerRepo) ListBanner(version, location string) ([]*dto.BannerDto, error) {
	var banners []*model.Banner

	// 默认只查询未删除和已启用的记录
	query := d.db.Model(&model.Banner{}).
		Where("is_delete = ?", model.StatusDisabled).
		Where("is_active = ?", model.StatusEnabled)

	if location != "" {
		// 查询对应位置的Banner
		query = query.Where("location = ?", location)
	}

	// 如果提供了版本号，则查询max_version_int大于等于该版本的int值的Banner
	if version != "" {
		versionInt := utils.VersionToVersionInt(version)
		query = query.Where("max_version_int >= ?", versionInt)
	}

	// 按排序和创建时间排序
	if err := query.Order("sort ASC, create_at DESC").Find(&banners).Error; err != nil {
		d.log.Error("查询Banner列表失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.BannerDto
	for _, banner := range banners {
		dtoList = append(dtoList, d.convertModel2Dto(banner))
	}

	return dtoList, nil
}

// UpdateBannerStatus 更新Banner状态
func (d *bannerRepo) UpdateBannerStatus(id int64, isActive int8, tx ...*gorm.DB) error {
	updates := map[string]any{
		"is_active": isActive,
	}

	if err := d.getDb(tx).Model(&model.Banner{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		d.log.Error("更新Banner状态错误: %v", err.Error())
		return err
	}
	return nil
}

// BatchDeleteBanner 批量删除Banner
func (d *bannerRepo) BatchDeleteBanner(ids []int64, tx ...*gorm.DB) error {
	updates := map[string]any{
		"is_delete": model.StatusEnabled,
	}

	if err := d.getDb(tx).Model(&model.Banner{}).Where("id IN (?)", ids).Updates(updates).Error; err != nil {
		d.log.Error("批量删除Banner错误: %v", err.Error())
		return err
	}
	return nil
}

// GetAllActiveBanners 获取所有启用的Banner
func (d *bannerRepo) GetAllActiveBanners(tx ...*gorm.DB) ([]*dto.BannerDto, error) {
	var banners []*model.Banner

	if err := d.getDb(tx).Model(&model.Banner{}).Where(map[string]any{
		"is_active": model.StatusEnabled,
		"is_delete": model.StatusDisabled,
	}).Order("sort ASC, create_at DESC").Find(&banners).Error; err != nil {
		d.log.Error("查询所有启用的Banner失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.BannerDto
	for _, banner := range banners {
		dtoList = append(dtoList, d.convertModel2Dto(banner))
	}

	return dtoList, nil
}

// BatchUpdateBanner 批量更新Banner
func (d *bannerRepo) BatchUpdateBanner(req *dto.BatchUpdateBannerRequest, tx ...*gorm.DB) error {
	if len(req.IDs) == 0 {
		return errors.New("banner ID列表不能为空")
	}

	var updates = make(map[string]any)

	if req.IsActive != 0 {
		updates["is_active"] = req.IsActive
	}

	if req.IsDelete != 0 {
		updates["is_delete"] = req.IsDelete
	}

	if req.MaxVersion != "" {
		updates["max_version"] = req.MaxVersion
		updates["max_version_int"] = utils.VersionToVersionInt(req.MaxVersion)
	}

	if req.Location != "" {
		updates["location"] = req.Location
	}

	if len(updates) == 0 {
		return nil
	}

	if err := d.getDb(tx).Model(&model.Banner{}).Where("id IN (?)", req.IDs).Updates(updates).Error; err != nil {
		d.log.Error("批量更新Banner错误: %v", err.Error())
		return err
	}
	return nil
}
