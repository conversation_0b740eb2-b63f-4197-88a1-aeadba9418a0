package aliyun

import (
	"fmt"
	"net"
	"os"
	"strings"
	"time"

	"chongli/component/apollo"
	"chongli/pkg/logger"
	"github.com/gin-gonic/gin"

	"github.com/aliyun/aliyun-log-go-sdk/producer"
	"github.com/tidwall/sjson"
)

// SLSConfig 配置
type SLSConfig struct {
	ProjectName     string
	Endpoint        string
	LogStoreName    string
	AccessKeyID     string
	AccessKeySecret string
	AllowLogLevel   string
	AddLogMap       map[string]string
}

type Callback struct {
}

// Success 调试时调用 SendLogWithCallBack 开启
func (callback *Callback) Success(result *producer.Result) {
	attemptList := result.GetReservedAttempts() // 遍历获得所有的发送记录
	for _, attempt := range attemptList {
		fmt.Println(attempt)
	}
}

// Fail 调试时调用 SendLogWithCallBack 开启
func (callback *Callback) Fail(result *producer.Result) {
	fmt.Println(result.IsSuccessful())        // 获得发送日志是否成功
	fmt.Println(result.GetErrorCode())        // 获得最后一次发送失败错误码
	fmt.Println(result.GetErrorMessage())     // 获得最后一次发送失败信息
	fmt.Println(result.GetReservedAttempts()) // 获得producerBatch 每次尝试被发送的信息
	fmt.Println(result.GetRequestId())        // 获得最后一次发送失败请求Id
	fmt.Println(result.GetTimeStampMs())      // 获得最后一次发送失败请求时间
}

// SetSLSConfig 设置日志参数
func (conf *SLSConfig) SetSLSConfig(context *gin.Context, request, response string) *SLSConfig {
	apolloConfig := apollo.GetApolloConfig()
	// sls 配置
	conf.Endpoint = apolloConfig.SLSEndpoint
	conf.ProjectName = apolloConfig.SLSProjectName
	conf.LogStoreName = apolloConfig.SLSLogStoreName
	conf.AccessKeyID = apolloConfig.SLSAccessKeyID
	conf.AccessKeySecret = apolloConfig.SLSAccessKeySecret
	conf.AllowLogLevel = apolloConfig.SLSAllowLogLevel
	contents := `{}`
	contents, _ = sjson.Set(contents, `request`, fmt.Sprintf("%v", request))
	contents, _ = sjson.Set(contents, `response`, fmt.Sprintf("%v", response))

	containerIps := getInterfaceAddress()
	osHostname, _ := os.Hostname()
	conf.AddLogMap = map[string]string{
		"content":          contents,
		"_container_ip_":   containerIps,
		"_container_name_": apolloConfig.SLSContainerName,
		"_pod_name_":       osHostname,
		"_image_name_":     apolloConfig.SLSImageName,
		"_source_":         apolloConfig.SLSSource,
		"_time_":           fmt.Sprintf("%v", time.Now().Format("2006-01-02 15:04:05")),
	}
	return conf
}

func getInterfaceAddress() string {
	ipAddress := make([]string, 0)
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, address := range addrs {
		// 检查ip地址判断是否回环地址
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				ipAddress = append(ipAddress, ipnet.IP.String())
			}
		}
	}
	return strings.Join(ipAddress, ";")
}

// Send 发送阿里云日志
func (conf *SLSConfig) Send() {
	producerConfig := producer.GetDefaultProducerConfig()
	producerConfig.AccessKeyID = conf.AccessKeyID
	producerConfig.AccessKeySecret = conf.AccessKeySecret
	if producerConfig.AccessKeyID == "" || producerConfig.AccessKeySecret == "" {
		return
	}
	producerConfig.Endpoint = conf.Endpoint
	producerConfig.AllowLogLevel = conf.AllowLogLevel
	producerInstance := producer.InitProducer(producerConfig)
	producerInstance.Start() // 启动producer实例
	newLogs := producer.GenerateLog(uint32(time.Now().Unix()), conf.AddLogMap)
	err := producerInstance.SendLog(conf.ProjectName, conf.LogStoreName, "delivery-platform", "topic", newLogs)
	if err != nil {
		logger.Log().LogCode("aliyun_sls", fmt.Sprintf("阿里云日志发送失败:[%v],参数:[%v]\n", err, conf.AddLogMap))
	}
	producerInstance.SafeClose()
}

// SLSWithRequestID SLS log with requestID.
func SLSWithRequestID(requestID string, tag string, lofInfo interface{}) {
	logger.Log().Info("请求日志：SLSWithRequestID requestID: %s, tag: %s, lofInfo: %+v", requestID, tag, lofInfo)
	// utils.Go(func() {
	// 	apolloConfig := apollo.GetApolloConfig()
	// 	// sls 配置
	// 	var conf = SLSConfig{}
	// 	conf.Endpoint = apolloConfig.SLSEndpoint
	// 	conf.ProjectName = apolloConfig.SLSProjectName
	// 	conf.LogStoreName = apolloConfig.SLSLogStoreName
	// 	conf.AccessKeyID = apolloConfig.SLSAccessKeyID
	// 	conf.AccessKeySecret = apolloConfig.SLSAccessKeySecret
	// 	conf.AllowLogLevel = apolloConfig.SLSAllowLogLevel
	// 	contents := `{}`
	// 	contents, _ = sjson.Set(contents, `request_id`, fmt.Sprintf("%v", requestID))
	// 	contents, _ = sjson.Set(contents, `flag`, fmt.Sprintf("%v", tag))
	// 	contents, _ = sjson.Set(contents, `log_info`, fmt.Sprintf("%v", lofInfo))
	// 	containerIps := getInterfaceAddress()
	// 	osHostname, _ := os.Hostname()
	// 	conf.AddLogMap = map[string]string{
	// 		"content":          contents,
	// 		"_container_ip_":   containerIps,
	// 		"_container_name_": apolloConfig.SLSContainerName,
	// 		"_pod_name_":       osHostname,
	// 		"_image_name_":     apolloConfig.SLSImageName,
	// 		"_source_":         apolloConfig.SLSSource,
	// 		"_time_":           fmt.Sprintf("%v", time.Now().Format("2006-01-02 15:04:05")),
	// 	}

	// 	producerConfig := producer.GetDefaultProducerConfig()
	// 	producerConfig.AccessKeyID = conf.AccessKeyID
	// 	producerConfig.AccessKeySecret = conf.AccessKeySecret
	// 	if producerConfig.AccessKeyID == "" || producerConfig.AccessKeySecret == "" {
	// 		return
	// 	}

	// 	producerConfig.Endpoint = conf.Endpoint
	// 	producerConfig.AllowLogLevel = conf.AllowLogLevel
	// 	producerInstance := producer.InitProducer(producerConfig)
	// 	producerInstance.Start() // 启动producer实例
	// 	newLogs := producer.GenerateLog(uint32(time.Now().Unix()), conf.AddLogMap)
	// 	err := producerInstance.SendLog(conf.ProjectName, conf.LogStoreName, "delivery-platform", "topic", newLogs)
	// 	if err != nil {
	// 		logger.Log().LogCode("aliyun_sls", fmt.Sprintf("阿里云日志发送失败:[%v],参数:[%v]\n", err, conf.AddLogMap))
	// 	}

	// 	producerInstance.SafeClose()
	// })

}
