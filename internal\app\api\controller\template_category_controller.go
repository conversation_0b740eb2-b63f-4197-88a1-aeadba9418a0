package controller

import (
	"chongli/component"
	"chongli/internal/app/api/biz"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type TemplateCategoryController struct {
	templateCategoryBiz *biz.TemplateCategoryBiz
	log                 *logger.Logger
}

func NewTemplateCategoryController(
	bootstrap *component.BootStrap,
	templateCategoryBiz *biz.TemplateCategoryBiz,
) *TemplateCategoryController {
	return &TemplateCategoryController{
		log:                 bootstrap.Log,
		templateCategoryBiz: templateCategoryBiz,
	}
}

func (c *TemplateCategoryController) GetCategoryWithTemplateList(ctx *gin.Context) {
	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("version is required"), response.WithSLSLog)
		return
	}

	mainClassIdStr := ctx.Param("id")
	mainClassId, _ := strconv.ParseInt(mainClassIdStr, 10, 64)
	if mainClassIdStr == "" || mainClassId <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("main class id is required"), response.WithSLSLog)
		return
	}

	// 处理业务逻辑
	data, err := c.templateCategoryBiz.GetCategoryWithTemplateList(ctx, version, mainClassId)
	if err != nil {
		c.log.Error("获取模板分类列表失败: %v", err)
		response.Response(ctx, nil, nil, err, response.WithSLSLog)
		return
	}

	// 响应
	response.Response(ctx, nil, data, nil, response.WithSLSLog)
}

// GetTemplateCategoryList 获取模板分类列表
func (c *TemplateCategoryController) GetTemplateCategoryList(ctx *gin.Context) {
	version := ctx.GetHeader("version")
	if version == "" {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("version is required"), response.WithSLSLog)
		return
	}

	mainClassIdStr := ctx.Param("id")
	mainClassId, _ := strconv.ParseInt(mainClassIdStr, 10, 64)
	if mainClassIdStr == "" || mainClassId <= 0 {
		response.Response(ctx, nil, nil, errpkg.NewMiddleError("main class id is required"), response.WithSLSLog)
		return
	}

	// 处理业务逻辑
	data, err := c.templateCategoryBiz.GetTemplateCategoryList(version, mainClassId)
	if err != nil {
		c.log.Error("获取模板分类列表失败: %v", err)
		response.Response(ctx, nil, nil, err, response.WithSLSLog)
		return
	}

	// 响应
	response.Response(ctx, nil, data, nil, response.WithSLSLog)
}
